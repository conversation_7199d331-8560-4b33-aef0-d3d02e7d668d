import '../../models/bot_config.dart';

/// Abstract base class for building bot payloads
/// Each bot type will have its own concrete implementation
abstract class BotPayloadBuilder {
  final BotConfig botConfig;
  final int exchangePairId;
  final int exchangeId;
  final String strategy;
  final bool isEditMode;
  final int? botId;

  BotPayloadBuilder({
    required this.botConfig,
    required this.exchangePairId,
    required this.exchangeId,
    required this.strategy,
    required this.isEditMode,
    this.botId,
  });

  /// Build the complete payload for the specific bot type
  Map<String, dynamic> buildPayload();

  /// Get bot type string
  String get botType;

  /// Parse percentage value from string like "50%" or "US$ 500 (50%)"
  double parsePercentageValue(String percentageStr) {
    final regex = RegExp(r'(\d+(?:\.\d+)?)%');
    final match = regex.firstMatch(percentageStr);
    if (match != null) {
      return double.tryParse(match.group(1) ?? '0') ?? 0.0;
    }
    return 0.0;
  }

  /// Parse initial fund from string like "US$ 1,000"
  double parseInitialFund(String fundAllocation) {
    final regex = RegExp(r'[\d,]+\.?\d*');
    final match = regex.firstMatch(fundAllocation);
    if (match != null) {
      final numStr = match.group(0)?.replaceAll(',', '') ?? '0';
      return double.tryParse(numStr) ?? 0.0;
    }
    return 0.0;
  }

  /// Parse number value from strings like "5 orders" to 5
  int parseNumberValue(String numberStr) {
    final regex = RegExp(r'\d+');
    final match = regex.firstMatch(numberStr);
    if (match != null) {
      return int.tryParse(match.group(0) ?? '1') ?? 1;
    }
    return 1;
  }

  /// Parse stop loss value
  double parseStopLoss(String stopLossStr) {
    final percentage = parsePercentageValue(stopLossStr);
    return percentage > 0 ? percentage : 5.0;
  }

  /// Parse minimum indicators required
  int parseMinIndicators(String minIndicatorsStr) {
    final regex = RegExp(r'\d+');
    final matches = regex.allMatches(minIndicatorsStr);
    if (matches.isNotEmpty) {
      return int.tryParse(matches.first.group(0) ?? '1') ?? 1;
    }
    return 1;
  }

  /// Format bot indicators for API
  List<Map<String, dynamic>> formatBotIndicators(BotConfig config) {
    List<Map<String, dynamic>> indicators = [];

    // Add entry indicators
    for (String indicator in config.entries.entryIndicators) {
      indicators.add(_parseIndicator(indicator));
    }

    // Add exit indicators
    for (String indicator in config.exits.exitIndicators) {
      indicators.add(_parseIndicator(indicator));
    }

    return indicators;
  }

  /// Parse individual indicator string
  Map<String, dynamic> _parseIndicator(String indicatorStr) {
    // Default indicator structure
    Map<String, dynamic> indicator = {
      'id': null,
      'name': indicatorStr,
      'description': null,
      'period_num': 20,
      'value2': 2,
      'value3': 2,
      'value4': null,
      'value5': null,
      'value6': null,
      'value7': null,
      'value8': null,
      'value9': null,
      'value10': null,
      'value11': null,
      'value12': null,
      'value13': null,
      'value14': null,
      'value15': null,
      'value16': null,
      'value17': null,
      'value18': null,
      'value19': null,
      'value20': null,
    };

    // Parse specific indicator types
    if (indicatorStr.contains('RSI')) {
      final regex = RegExp(r'RSI.*?(\d+).*?(\d+).*?(\d+)');
      final match = regex.firstMatch(indicatorStr);
      if (match != null) {
        indicator['name'] = 'RSI';
        indicator['period_num'] = int.tryParse(match.group(1) ?? '14') ?? 14;
        indicator['value2'] = int.tryParse(match.group(2) ?? '30') ?? 30;
        indicator['value3'] = int.tryParse(match.group(3) ?? '70') ?? 70;
      }
    } else if (indicatorStr.contains('Bollinger Band')) {
      final regex = RegExp(r'Bollinger Band.*?(\d+).*?(\d+(?:\.\d+)?)');
      final match = regex.firstMatch(indicatorStr);
      if (match != null) {
        indicator['name'] = 'Bollinger Band';
        indicator['period_num'] = int.tryParse(match.group(1) ?? '20') ?? 20;
        indicator['value2'] = double.tryParse(match.group(2) ?? '2') ?? 2;
        indicator['value3'] = double.tryParse(match.group(2) ?? '2') ?? 2;
      }
    }

    return indicator;
  }

  /// Get base order type value
  String getBaseOrderTypeValue() {
    final originalType = botConfig.entries.baseOrderType;
    final convertedType =
        originalType.toLowerCase() == 'static' ? 'static' : 'percentage';

    print('=== BASE ORDER TYPE DEBUG ===');
    print('Original base order type: $originalType');
    print('Converted base order type: $convertedType');
    print('=============================');

    return convertedType;
  }

  /// Get frequency value
  int getFrequencyValue() {
    switch (botConfig.summary.frequency) {
      case '1m':
        return 1;
      case '5m':
        return 5;
      case '15m':
        return 15;
      case '30m':
        return 30;
      case '1H':
        return 60;
      case '4H':
        return 240;
      case '1D':
        return 1440;
      default:
        return 15;
    }
  }

  /// Get order type value
  String getOrderTypeValue(String orderType) {
    return orderType.toLowerCase() == 'market' ? 'market' : 'limit';
  }
}
