import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:stockhero/models/bot_config.dart';
import 'package:stockhero/services/bot_service.dart';
import 'package:stockhero/shared/controllers/auth_controller.dart';
import 'package:stockhero/shared/providers/bot_config_provider.dart';
import 'package:stockhero/shared/utils/theme_reflector.dart';
import 'package:stockhero/shared/utils/theme_utils.dart';
import "package:stockhero/utils/constants.dart";

class BotAssetScreen extends StatefulWidget {
  const BotAssetScreen({Key? key}) : super(key: key);

  @override
  State<BotAssetScreen> createState() => _BotAssetScreenState();
}

class _BotAssetScreenState extends State<BotAssetScreen> {
  bool isLongStrategy = true;
  final TextEditingController _botNameController = TextEditingController();
  final TextEditingController _symbolSearchController = TextEditingController();
  String selectedSymbol = 'GOOG/USD';
  String selectedBrokerage = 'Paper';
  int? selectedExchangeId = 0;
  int selectedPairId = 1;
  String baseAsset = 'AAPL';
  String quoteAsset = 'USD';
  double currentPrice = 0;
  int? botId;
  bool isEditing = false;
  bool isCopying = false;
  bool isLoading = false;
  String? errorMessage;
  String botType = 'advanced'; // Default to advanced
  final BotService _botService = BotService();
  List<Map<String, dynamic>> exchanges = [];
  List<Map<String, dynamic>> tradingSymbols = [];
  List<Map<String, dynamic>> userBalances = [];
  bool isLoadingExchanges = false;
  bool isLoadingSymbols = false;
  bool isLoadingBalances = false;
  bool isSearchingSymbols = false;
  String? _baseImageUrl;

  // Thêm controller để điều khiển scroll view
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _searchFieldKey = GlobalKey();

  // Lưu trữ dữ liệu bot đầy đủ từ API
  Map<String, dynamic> fullBotData = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _processRouteArguments();
      _fetchExchanges();
      _symbolSearchController.addListener(_onSymbolSearchChanged);
    });
  }

  void _processRouteArguments() {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      setState(() {
        botId = args['botId'];
        isEditing = args['isEditing'] == true;
        isCopying = args['isCopying'] == true;
        botType = args['botType'] ?? 'advanced'; // Get botType from arguments
      });

      if (botId != null && (isEditing || isCopying)) {
        _fetchBotDetails(botId!).then((_) {
          // ✅ Restore strategy AFTER fetching bot details (for new bots)
          if (!isEditing && !isCopying && args.containsKey('strategy')) {
            final strategyFromArgs = args['strategy'];
            if (strategyFromArgs != null) {
              setState(() {
                isLongStrategy =
                    strategyFromArgs.toString().toLowerCase() == 'long';
              });
            }
          }
        });
      } else {
        // ✅ For new bots (no botId), restore strategy if available
        // This handles navigation from other screens with strategy args
        if (args.containsKey('strategy')) {
          final strategyFromArgs = args['strategy'];
          if (strategyFromArgs != null) {
            setState(() {
              isLongStrategy =
                  strategyFromArgs.toString().toLowerCase() == 'long';
            });
          }
        }
        // If no strategy in args, keep default (true = Long)
      }
    }
  }

  Future<void> _fetchBotDetails(int id) async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final authController =
          Provider.of<AuthController>(context, listen: false);
      final token = authController.token;

      if (token == null) {
        setState(() {
          errorMessage = 'Authentication error. Please log in again.';
          isLoading = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse(
            '${AppConstants.apiBaseUrl}${AppConstants.getBotByIdEndpoint}$id'),
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.apiTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['code'] == 200 && data['bot'] != null) {
          final botData = data['bot'] as Map<String, dynamic>;

          // Lưu dữ liệu bot đầy đủ
          fullBotData = botData;

          // Cập nhật UI với thông tin bot
          setState(() {
            // Nếu đang copy bot, thêm "Copy of" vào tên
            if (isCopying) {
              _botNameController.text = "Copy of ${botData['name']}";
            } else {
              _botNameController.text = botData['name'] ?? '';
            }

            // Cập nhật chiến lược (Long/Short)
            isLongStrategy =
                (botData['strategy'] ?? 'Long').toLowerCase() == 'long';

            // Cập nhật exchange_pair
            if (botData['exchange_pair'] != null) {
              selectedSymbol = botData['exchange_pair']['name'] ?? 'GOOG/USD';
              selectedPairId = botData['pair_id'] ?? 1;

              // Lấy thông tin từ pair nếu có
              if (botData['exchange_pair']['pair'] != null) {
                baseAsset = botData['exchange_pair']['pair']['from'] ?? '';
                quoteAsset = botData['exchange_pair']['pair']['to'] ?? '';

                // Lưu url hình ảnh
                _baseImageUrl = botData['exchange_pair']['pair']['base_image'];
              }

              // Cập nhật brokerage
              if (botData['exchange_pair']['exchange'] != null) {
                selectedBrokerage =
                    botData['exchange_pair']['exchange']['name'] ?? 'Paper';
                selectedExchangeId = botData['exchange_id'] ?? 0;

                // Gọi API lấy thông tin chi tiết trading symbol
                _fetchTradingSymbolInfo(selectedExchangeId!, selectedPairId);
              }
            }

            isLoading = false;
          });

          print('Bot details loaded successfully: ${botData['name']}');
        } else {
          setState(() {
            errorMessage = data['message'] ?? 'Failed to load bot details';
            isLoading = false;
          });
        }
      } else {
        setState(() {
          errorMessage =
              'Failed to load bot details. Status code: ${response.statusCode}';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Error loading bot details: $e';
        isLoading = false;
      });
      print('Exception in _fetchBotDetails: $e');
    }
  }

  Future<void> _fetchExchanges() async {
    setState(() {
      isLoadingExchanges = true;
    });

    try {
      final authController =
          Provider.of<AuthController>(context, listen: false);
      final token = authController.token;

      if (token == null) {
        print('Authentication token not found');
        setState(() {
          isLoadingExchanges = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse(
            '${AppConstants.apiBaseUrl}${AppConstants.getExchangesEndpoint}'),
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.apiTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['code'] == 200 && data['exchanges'] != null) {
          final exchangesList = data['exchanges'] as List;

          // Lọc chỉ lấy các exchange đã kết nối và đang hoạt động
          final connectedExchanges = exchangesList
              .where((exchange) =>
                  exchange['is_connected'] == true && exchange['active'] == 1)
              .map((e) => e as Map<String, dynamic>)
              .toList();

          setState(() {
            exchanges = connectedExchanges;
            isLoadingExchanges = false;

            // Nếu chưa chọn exchange nào, thì chọn cái đầu tiên
            if (selectedExchangeId == null && connectedExchanges.isNotEmpty) {
              selectedExchangeId = connectedExchanges[0]['id'];
              selectedBrokerage = connectedExchanges[0]['name'];

              // Sau khi chọn exchange, gọi API lấy thông tin trading symbol
              _fetchTradingSymbolInfo(selectedExchangeId!, selectedPairId);
              _fetchBalances(selectedExchangeId!);
            }
          });

          print('Loaded ${connectedExchanges.length} connected exchanges');
        } else {
          print('Failed to load exchanges: ${data['message']}');
          setState(() {
            isLoadingExchanges = false;
          });
        }
      } else {
        print('Failed to load exchanges. Status code: ${response.statusCode}');
        setState(() {
          isLoadingExchanges = false;
        });
      }
    } catch (e) {
      print('Exception in _fetchExchanges: $e');
      setState(() {
        isLoadingExchanges = false;
      });
    }
  }

  Future<void> _fetchTradingSymbolInfo(int exchangeId, int pairId) async {
    setState(() {
      isLoadingSymbols = true;
    });

    try {
      final authController =
          Provider.of<AuthController>(context, listen: false);
      final token = authController.token;

      if (token == null) {
        print('Authentication token not found');
        setState(() {
          isLoadingSymbols = false;
        });
        return;
      }

      // Tạo query params
      final Map<String, String> queryParams = {
        'exchange_id': exchangeId.toString(),
        'pair_id': pairId.toString(),
        'account': 'spot',
      };

      // Tạo URL với query params
      final uri = Uri.parse('${AppConstants.apiBaseUrl}/api/account')
          .replace(queryParameters: queryParams);

      print('Fetching trading symbol info: $uri');

      final response = await http.get(
        uri,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.apiTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('Trading symbol response: ${response.body}');

        if (data['code'] == 200) {
          String oldBaseAsset = baseAsset;
          String oldQuoteAsset = quoteAsset;

          setState(() {
            if (data['base'] != null && data['base']['coin_name'] != null) {
              baseAsset = data['base']['coin_name'];
            }

            if (data['quote'] != null && data['quote']['coin_name'] != null) {
              quoteAsset = data['quote']['coin_name'];
            }

            // Cập nhật selectedSymbol - chỉ cập nhật nếu cả baseAsset và quoteAsset đều có giá trị
            if (baseAsset.isNotEmpty && quoteAsset.isNotEmpty) {
              selectedSymbol = '$baseAsset/$quoteAsset';
            }

            // Lưu giá hiện tại nếu có
            if (data['current_price'] != null) {
              currentPrice =
                  double.tryParse(data['current_price'].toString()) ?? 0;
            }

            isLoadingSymbols = false;
          });

          // Log changes for debugging
          print('UPDATED trading symbol data:');
          print('Base Asset: $oldBaseAsset -> $baseAsset');
          print('Quote Asset: $oldQuoteAsset -> $quoteAsset');
          print('Selected Symbol: $selectedSymbol');
          print('Current Price: $currentPrice');

          // Sau khi cập nhật thông tin trading symbol, lấy thêm thông tin về hình ảnh nếu cần
          _fetchSymbolImage(baseAsset, quoteAsset);
        } else {
          print('Failed to load trading symbol info: ${data['message']}');
          setState(() {
            isLoadingSymbols = false;
          });
        }
      } else {
        print(
            'Failed to load trading symbol info. Status code: ${response.statusCode}');
        setState(() {
          isLoadingSymbols = false;
        });
      }
    } catch (e) {
      print('Exception in _fetchTradingSymbolInfo: $e');
      setState(() {
        isLoadingSymbols = false;
      });
    }
  }

  // Hàm lấy hình ảnh cho symbol
  Future<void> _fetchSymbolImage(String baseAsset, String quoteAsset) async {
    // Tạo truy vấn đơn giản để tìm kiếm
    final searchQuery = baseAsset;

    try {
      if (selectedExchangeId == null) return;

      final authController =
          Provider.of<AuthController>(context, listen: false);
      final token = authController.token;

      if (token == null) return;

      // Tạo query params
      final Map<String, String> queryParams = {
        'exchange_id': selectedExchangeId.toString(),
        'symbol': searchQuery,
      };

      final uri = Uri.parse(
              '${AppConstants.apiBaseUrl}${AppConstants.getExchangePairListEndpoint}')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.apiTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['code'] == 200) {
          // Tìm symbol phù hợp
          data.forEach((key, value) {
            if (key != 'code' &&
                key != 'message' &&
                key != 'exchange' &&
                key != 'transaction_fee' &&
                value is List) {
              for (var symbol in value) {
                if (symbol is Map<String, dynamic> &&
                    symbol['pair'] != null &&
                    symbol['pair']['from'] == baseAsset &&
                    symbol['pair']['to'] == quoteAsset) {
                  setState(() {
                    _baseImageUrl = symbol['pair']['base_image'];
                  });
                  return;
                }
              }
            }
          });
        }
      }
    } catch (e) {
      print('Exception in _fetchSymbolImage: $e');
    }
  }

  Future<void> _fetchBalances(int exchangeId) async {
    setState(() {
      isLoadingBalances = true;
    });

    try {
      final authController =
          Provider.of<AuthController>(context, listen: false);
      final token = authController.token;

      if (token == null) {
        print('Authentication token not found');
        setState(() {
          isLoadingBalances = false;
        });
        return;
      }

      // Tạo query params
      final Map<String, String> queryParams = {
        'exchange_id': exchangeId.toString(),
      };

      final uri = Uri.parse(
              '${AppConstants.apiBaseUrl}${AppConstants.getBalancesEndpoint}')
          .replace(queryParameters: queryParams);

      print('Fetching balances: $uri');

      final response = await http.get(
        uri,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.apiTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['code'] == 200 &&
            data['exchange'] != null &&
            data['exchange']['user_balances'] != null) {
          final balancesList = data['exchange']['user_balances'] as List;

          // Lọc các balance có số dư > 0
          final nonZeroBalances = balancesList
              .where((balance) =>
                  (double.tryParse(balance['total'].toString()) ?? 0) > 0)
              .map((e) => e as Map<String, dynamic>)
              .toList();

          setState(() {
            userBalances = nonZeroBalances;
            isLoadingBalances = false;
          });

          print('Loaded ${nonZeroBalances.length} non-zero balances');
        } else {
          print('Failed to load balances: ${data['message']}');
          setState(() {
            isLoadingBalances = false;
          });
        }
      } else {
        print('Failed to load balances. Status code: ${response.statusCode}');
        setState(() {
          isLoadingBalances = false;
        });
      }
    } catch (e) {
      print('Exception in _fetchBalances: $e');
      setState(() {
        isLoadingBalances = false;
      });
    }
  }

  @override
  void dispose() {
    _botNameController.dispose();
    _symbolSearchController.removeListener(_onSymbolSearchChanged);
    _symbolSearchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Helper method to get asset image path based on bot type
  String _getAssetImagePath(String? botType) {
    switch (botType?.toLowerCase()) {
      case 'grid':
        return 'assets/images/grid_asset.png';
      case 'momentum':
        return 'assets/images/momentum_asset.png';
      default:
        return 'assets/images/trading_asset.png';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeReflector.screenBackground(context),
      appBar: AppBar(
        backgroundColor: ThemeReflector.surfaceColor(context),
        elevation: 0,
        title: Text(
          isEditing ? 'Edit Bot' : (isCopying ? 'Copy Bot' : 'Create Bot'),
          style: TextStyle(
            color: ThemeReflector.textColor(
              context,
              importance: TextImportance.primary,
            ),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: ThemeReflector.iconColor(context),
            size: 24,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.close,
              color: ThemeReflector.iconColor(context),
              size: 24,
            ),
            onPressed: () => Navigator.pushNamedAndRemoveUntil(
              context,
              AppConstants.dashboardRoute,
              (route) => false,
            ),
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : (errorMessage != null
              ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline,
                            color: Theme.of(context).colorScheme.error,
                            size: 48),
                        const SizedBox(height: 16),
                        Text(
                          errorMessage!,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: Theme.of(context).colorScheme.error),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () {
                            if (botId != null) {
                              _fetchBotDetails(botId!);
                            } else {
                              Navigator.of(context).pop();
                            }
                          },
                          child: const Text('Try Again'),
                        ),
                      ],
                    ),
                  ),
                )
              : Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Asset image and title
                            Center(
                              child: Column(
                                children: [
                                  const SizedBox(height: 16),
                                  Image.asset(
                                    _getAssetImagePath(botType),
                                    width: 120,
                                    height: 120,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        width: 120,
                                        height: 120,
                                        decoration: BoxDecoration(
                                          color: ThemeReflector.surfaceColor(
                                              context),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.auto_awesome,
                                              size: 48,
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .primary,
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              isEditing
                                                  ? "Edit Bot"
                                                  : (isCopying
                                                      ? "Copy Bot"
                                                      : "Create Bot"),
                                              style: TextStyle(
                                                color: ThemeReflector.textColor(
                                                  context,
                                                  importance:
                                                      TextImportance.primary,
                                                ),
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                  const SizedBox(height: 16),
                                ],
                              ),
                            ),
                            // Asset title
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Text(
                                "Asset",
                                style: TextStyle(
                                  color: ThemeReflector.textColor(
                                    context,
                                    importance: TextImportance.primary,
                                  ),
                                  fontFamily: 'Inter',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  letterSpacing: -0.5,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            // Step indicator
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: Row(
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Container(
                                                  height: 6,
                                                  decoration: BoxDecoration(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .primary,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            3),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: 4),
                                              Expanded(
                                                flex: 1,
                                                child: Container(
                                                  height: 6,
                                                  decoration: BoxDecoration(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .primary
                                                        .withOpacity(0.2),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            3),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: 4),
                                              Expanded(
                                                flex: 1,
                                                child: Container(
                                                  height: 6,
                                                  decoration: BoxDecoration(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .primary
                                                        .withOpacity(0.2),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            3),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: 4),
                                              Expanded(
                                                flex: 1,
                                                child: Container(
                                                  height: 6,
                                                  decoration: BoxDecoration(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .primary
                                                        .withOpacity(0.2),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            3),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: 4),
                                              Expanded(
                                                flex: 1,
                                                child: Container(
                                                  height: 6,
                                                  decoration: BoxDecoration(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .primary
                                                        .withOpacity(0.2),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            3),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Bot Name Input
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: TextField(
                                controller: _botNameController,
                                decoration: InputDecoration(
                                  hintText: 'Enter Bot Name',
                                  hintStyle: TextStyle(
                                    color: ThemeReflector.textColor(
                                      context,
                                      importance: TextImportance.tertiary,
                                    ),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  filled: true,
                                  fillColor:
                                      ThemeReflector.surfaceColor(context),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 14,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color:
                                          ThemeReflector.borderColor(context),
                                      width: 1,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color:
                                          ThemeReflector.borderColor(context),
                                      width: 1,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      width: 1,
                                    ),
                                  ),
                                ),
                              ),
                            ),

                            // Brokerage
                            _buildSectionWithHeader(
                              "Brokerage",
                              InkWell(
                                onTap: () {
                                  _showExchangeSelector();
                                },
                                child: Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 12),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color:
                                          ThemeReflector.borderColor(context),
                                      width: 1,
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        selectedBrokerage,
                                        style: TextStyle(
                                          color: ThemeReflector.textColor(
                                            context,
                                            importance: TextImportance.primary,
                                          ),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      Icon(
                                        Icons.chevron_right,
                                        color: ThemeReflector.iconColor(context,
                                            opacity: 0.7),
                                        size: 24,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),

                            // Trading Symbol
                            _buildSectionWithHeader(
                              "Trading Symbol",
                              Column(
                                children: [
                                  InkWell(
                                    onTap: () {
                                      // Hiện dialog khi nhấn vào trading symbol
                                      _showSymbolSelector();
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 12),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: ThemeReflector.borderColor(
                                              context),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Row(
                                            children: [
                                              isLoadingSymbols
                                                  ? Container(
                                                      width: 24,
                                                      height: 24,
                                                      padding:
                                                          const EdgeInsets.all(
                                                              4),
                                                      decoration: BoxDecoration(
                                                        color: ThemeReflector
                                                            .surfaceColor(
                                                                context),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                      ),
                                                      child:
                                                          CircularProgressIndicator(
                                                        strokeWidth: 2,
                                                        valueColor:
                                                            AlwaysStoppedAnimation<
                                                                Color>(Theme.of(
                                                                    context)
                                                                .colorScheme
                                                                .primary),
                                                      ),
                                                    )
                                                  : Container(
                                                      width: 24,
                                                      height: 24,
                                                      decoration: BoxDecoration(
                                                        color: ThemeReflector
                                                            .surfaceColor(
                                                                context),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                        image: _baseImageUrl !=
                                                                    null &&
                                                                _baseImageUrl!
                                                                    .isNotEmpty
                                                            ? DecorationImage(
                                                                image: NetworkImage(
                                                                    _baseImageUrl!),
                                                                fit: BoxFit
                                                                    .cover,
                                                              )
                                                            : null,
                                                      ),
                                                      child:
                                                          _baseImageUrl ==
                                                                      null ||
                                                                  _baseImageUrl!
                                                                      .isEmpty
                                                              ? Center(
                                                                  child: Text(
                                                                    baseAsset
                                                                            .isNotEmpty
                                                                        ? baseAsset.substring(
                                                                            0,
                                                                            baseAsset.length > 1
                                                                                ? 1
                                                                                : baseAsset.length)
                                                                        : 'G',
                                                                    style:
                                                                        TextStyle(
                                                                      color: ThemeReflector
                                                                          .textColor(
                                                                        context,
                                                                        importance:
                                                                            TextImportance.primary,
                                                                      ),
                                                                      fontSize:
                                                                          14,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                    ),
                                                                  ),
                                                                )
                                                              : null,
                                                    ),
                                              const SizedBox(width: 10),
                                              Text(
                                                selectedSymbol,
                                                style: TextStyle(
                                                  color:
                                                      ThemeReflector.textColor(
                                                    context,
                                                    importance:
                                                        TextImportance.primary,
                                                  ),
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              if (currentPrice > 0)
                                                Container(
                                                  margin: const EdgeInsets.only(
                                                      left: 8),
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 6,
                                                      vertical: 2),
                                                  decoration: BoxDecoration(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .surface,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            4),
                                                  ),
                                                  child: Text(
                                                    currentPrice.toString(),
                                                    style: TextStyle(
                                                      color: ThemeReflector
                                                          .textColor(
                                                        context,
                                                        importance:
                                                            TextImportance
                                                                .secondary,
                                                      ),
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                          Icon(
                                            Icons.chevron_right,
                                            color: ThemeReflector.iconColor(
                                                context,
                                                opacity: 0.7),
                                            size: 24,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Trading Strategy (ẩn cho Grid bot và Quickstart bot)
                            if (botType.toLowerCase() != 'grid' &&
                                botType.toLowerCase() != 'quickstart')
                              Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                          "Trading Strategy",
                                          style: TextStyle(
                                            color: ThemeReflector.textColor(
                                              context,
                                              importance:
                                                  TextImportance.tertiary,
                                            ),
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Container(
                                          width: 16,
                                          height: 16,
                                          decoration: const BoxDecoration(
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(
                                            Icons.info_outline,
                                            color: ThemeReflector.iconColor(
                                                context,
                                                opacity: 0.6),
                                            size: 16,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        // Long option
                                        Expanded(
                                          child: InkWell(
                                            onTap: () {
                                              setState(() {
                                                isLongStrategy = true;
                                              });
                                            },
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 12),
                                              decoration: BoxDecoration(
                                                color: isLongStrategy
                                                    ? const Color(0xFF14B377)
                                                        .withOpacity(0.1)
                                                    : Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                border: Border.all(
                                                  color: isLongStrategy
                                                      ? const Color(0xFF14B377)
                                                      : ThemeReflector
                                                          .borderColor(context),
                                                  width: 1,
                                                ),
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    width: 20,
                                                    height: 20,
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      border: Border.all(
                                                        color: isLongStrategy
                                                            ? const Color(
                                                                0xFF14B377)
                                                            : ThemeReflector
                                                                .textColor(
                                                                context,
                                                                importance:
                                                                    TextImportance
                                                                        .tertiary,
                                                              ),
                                                        width: 2,
                                                      ),
                                                    ),
                                                    child: isLongStrategy
                                                        ? const Center(
                                                            child: Icon(
                                                              Icons.circle,
                                                              size: 10,
                                                              color: Color(
                                                                  0xFF14B377),
                                                            ),
                                                          )
                                                        : null,
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Text(
                                                    "Long",
                                                    style: TextStyle(
                                                      color: isLongStrategy
                                                          ? const Color(
                                                              0xFF14B377)
                                                          : ThemeReflector
                                                              .textColor(
                                                              context,
                                                              importance:
                                                                  TextImportance
                                                                      .primary,
                                                            ),
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        // Short option
                                        Expanded(
                                          child: InkWell(
                                            onTap: () {
                                              setState(() {
                                                isLongStrategy = false;
                                              });
                                            },
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 12),
                                              decoration: BoxDecoration(
                                                color: !isLongStrategy
                                                    ? const Color(0xFFF16A6C)
                                                        .withOpacity(0.1)
                                                    : Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                border: Border.all(
                                                  color: !isLongStrategy
                                                      ? const Color(0xFFF16A6C)
                                                      : ThemeReflector
                                                          .borderColor(context),
                                                  width: 1,
                                                ),
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    width: 20,
                                                    height: 20,
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      border: Border.all(
                                                        color: !isLongStrategy
                                                            ? const Color(
                                                                0xFFF16A6C)
                                                            : ThemeReflector
                                                                .textColor(
                                                                context,
                                                                importance:
                                                                    TextImportance
                                                                        .tertiary,
                                                              ),
                                                        width: 2,
                                                      ),
                                                    ),
                                                    child: !isLongStrategy
                                                        ? const Center(
                                                            child: Icon(
                                                              Icons.circle,
                                                              size: 10,
                                                              color: Color(
                                                                  0xFFF16A6C),
                                                            ),
                                                          )
                                                        : null,
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Text(
                                                    "Short",
                                                    style: TextStyle(
                                                      color: !isLongStrategy
                                                          ? const Color(
                                                              0xFFF16A6C)
                                                          : ThemeReflector
                                                              .textColor(
                                                              context,
                                                              importance:
                                                                  TextImportance
                                                                      .primary,
                                                            ),
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),

                    // Bottom next button
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: ThemeReflector.surfaceColor(context),
                        border: Border(
                          top: BorderSide(
                            color: ThemeReflector.dividerColor(context),
                            width: 1,
                          ),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color:
                                Theme.of(context).shadowColor.withOpacity(0.1),
                            spreadRadius: 0,
                            blurRadius: 10,
                            offset: const Offset(0, -4),
                          ),
                        ],
                      ),
                      child: SizedBox(
                        width: double.infinity,
                        height: 48,
                        child: ElevatedButton(
                          onPressed: () {
                            final botName = _botNameController.text.trim();
                            if (botName.isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                    content: Text('Please enter a bot name')),
                              );
                              return;
                            }

                            // Validate that trading symbol is selected
                            if (selectedSymbol.isEmpty ||
                                baseAsset.isEmpty ||
                                quoteAsset.isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                    content:
                                        Text('Please select a trading symbol')),
                              );
                              return;
                            }

                            // Initialize the BotConfigProvider with basic information
                            final botConfigProvider =
                                Provider.of<BotConfigProvider>(context,
                                    listen: false);

                            // Create a summary with the user's inputs
                            final summary = BotSummary(
                              botName: botName,
                              brokerage: selectedBrokerage,
                              tradingPair: selectedSymbol,
                              frequency:
                                  '15m', // Default frequency, will be updated in trade parameters screen
                              fundAllocation:
                                  'US\$ 1,000', // Default amount, will be updated in trade parameters screen
                              tradeExtendedHour:
                                  true, // Default value, will be updated in trade parameters screen
                            );

                            // Update the provider with the summary
                            botConfigProvider.updateSummary(summary);

                            // Navigation data ready

                            // Truyền các tham số vào màn hình tiếp theo
                            Map<String, dynamic> args = {
                              'botName': botName,
                              'brokerage': selectedBrokerage,
                              'exchangeId': selectedExchangeId,
                              'pairId': selectedPairId,
                              'symbol': selectedSymbol,
                              'baseAsset': baseAsset,
                              'quoteAsset': quoteAsset,
                              'currentPrice': currentPrice,
                              'strategy': (botType.toLowerCase() == 'grid' ||
                                      botType.toLowerCase() == 'quickstart')
                                  ? 'Long' // Grid và Quickstart bot luôn là Long
                                  : (isLongStrategy ? 'Long' : 'Short'),
                              'botId': isEditing ? botId : null,
                              'isEditing': isEditing,
                              'isCopying': isCopying,
                              'botType': botType, // Pass botType to next screen
                              // Thêm các thông số từ response để hiển thị ở màn hình tiếp theo
                              'initialFund': isEditing || isCopying
                                  ? (fullBotData['initial_fund'] ?? 1000)
                                  : 1000,
                              'frequency': isEditing || isCopying
                                  ? (fullBotData['frequency'] ?? 15)
                                  : 15,
                              'baseOrderPercentage': isEditing || isCopying
                                  ? (fullBotData['base_order_percentage'] ?? 50)
                                  : 50,
                              'baseOrderType': isEditing || isCopying
                                  ? (fullBotData['base_order_type'] ?? 'static')
                                  : 'static',
                              'extraOrderPercentage': isEditing || isCopying
                                  ? (fullBotData['extra_order_percentage'] ??
                                      25)
                                  : 25,
                              'minPriceGap': isEditing || isCopying
                                  ? (fullBotData['extra_price_gap_min'] ?? 2.5)
                                  : 2.5,
                              'extendedTradingHour': isEditing || isCopying
                                  ? (fullBotData['extended_trading_hour'] ==
                                      '1')
                                  : true,
                              'orderTypeEntry': isEditing || isCopying
                                  ? (fullBotData['order_type'] ?? 'market')
                                  : 'market',
                              'orderTypeExit': isEditing || isCopying
                                  ? (fullBotData['order_type_exit'] ?? 'market')
                                  : 'market',
                            };

                            Navigator.pushNamed(
                              context,
                              AppConstants.tradeParameterRoute,
                              arguments: args,
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'Next',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                )),
    );
  }

  Widget _buildSectionWithHeader(String title, Widget content) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.tertiary,
                  ),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                width: 16,
                height: 16,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.info_outline,
                  color: ThemeReflector.iconColor(context, opacity: 0.6),
                  size: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          content,
        ],
      ),
    );
  }

  void _showExchangeSelector() {
    if (exchanges.isEmpty && !isLoadingExchanges) {
      _fetchExchanges();
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ThemeReflector.surfaceColor(context),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.3,
          maxChildSize: 0.8,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Column(
                    children: [
                      // Handle line
                      Container(
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: ThemeReflector.borderColor(context),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Title
                      Text(
                        'Select Brokerage',
                        style: TextStyle(
                          color: ThemeReflector.textColor(
                            context,
                            importance: TextImportance.primary,
                          ),
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: isLoadingExchanges
                      ? const Center(child: CircularProgressIndicator())
                      : exchanges.isEmpty
                          ? Center(
                              child: Text(
                                'No connected brokerages found',
                                style: TextStyle(
                                  color: ThemeReflector.textColor(
                                    context,
                                    importance: TextImportance.secondary,
                                  ),
                                ),
                              ),
                            )
                          : ListView.builder(
                              controller: scrollController,
                              itemCount: exchanges.length,
                              itemBuilder: (context, index) {
                                final exchange = exchanges[index];
                                final isSelected =
                                    selectedBrokerage == exchange['name'];

                                return InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                    setState(() {
                                      selectedBrokerage = exchange['name'];
                                      selectedExchangeId = exchange['id'];
                                      _symbolSearchController.clear();
                                      tradingSymbols.clear();

                                      // Sau khi chọn exchange, gọi API lấy thông tin
                                      _fetchTradingSymbolInfo(
                                          selectedExchangeId!, selectedPairId);
                                      _fetchBalances(selectedExchangeId!);
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 12),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withOpacity(0.1)
                                          : Colors.transparent,
                                      border: Border(
                                        bottom: BorderSide(
                                          color: ThemeReflector.borderColor(
                                              context),
                                          width: 1,
                                        ),
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        // Exchange image
                                        Container(
                                          width: 32,
                                          height: 32,
                                          decoration: BoxDecoration(
                                            color: ThemeReflector.surfaceColor(
                                                context),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            image: exchange['image'] != null &&
                                                    exchange['image']
                                                        .toString()
                                                        .isNotEmpty
                                                ? DecorationImage(
                                                    image: NetworkImage(
                                                        exchange['image']),
                                                    fit: BoxFit.contain,
                                                  )
                                                : null,
                                          ),
                                          child: exchange['image'] == null ||
                                                  exchange['image']
                                                      .toString()
                                                      .isEmpty
                                              ? Icon(
                                                  Icons.account_balance,
                                                  color:
                                                      ThemeReflector.iconColor(
                                                          context),
                                                  size: 18,
                                                )
                                              : null,
                                        ),
                                        const SizedBox(width: 12),
                                        // Exchange name
                                        Expanded(
                                          child: Text(
                                            exchange['name'] ?? 'Unknown',
                                            style: TextStyle(
                                              color: ThemeReflector.textColor(
                                                context,
                                                importance:
                                                    TextImportance.primary,
                                              ),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                        // Selected indicator
                                        if (isSelected)
                                          Icon(
                                            Icons.check_circle,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                            size: 20,
                                          ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showSymbolSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ThemeReflector.surfaceColor(context),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return StatefulBuilder(builder: (context, setModalState) {
          return DraggableScrollableSheet(
            initialChildSize: 0.8,
            minChildSize: 0.5,
            maxChildSize: 0.95,
            expand: false,
            builder: (context, scrollController) {
              return Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Column(
                        children: [
                          // Handle line
                          Container(
                            width: 40,
                            height: 4,
                            decoration: BoxDecoration(
                              color: ThemeReflector.borderColor(context),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Title
                          Text(
                            'Select Trading Symbol',
                            style: TextStyle(
                              color: ThemeReflector.textColor(
                                context,
                                importance: TextImportance.primary,
                              ),
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Search box
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: TextField(
                        controller: _symbolSearchController,
                        decoration: InputDecoration(
                          hintText: 'Search symbol...',
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: _symbolSearchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    setModalState(() {
                                      _symbolSearchController.clear();
                                      tradingSymbols.clear();
                                    });
                                  },
                                )
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 16),
                        ),
                        onChanged: (value) {
                          if (value.trim().length >= 2) {
                            _searchSymbols(value).then((_) {
                              setModalState(() {});
                            });
                          } else if (value.isEmpty) {
                            setModalState(() {
                              tradingSymbols.clear();
                            });
                          }
                        },
                        autofocus: true,
                      ),
                    ),

                    // Available balances section
                    if (userBalances.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 16, right: 16, bottom: 8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Your Assets',
                              style: TextStyle(
                                color: ThemeReflector.textColor(
                                  context,
                                  importance: TextImportance.secondary,
                                ),
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: userBalances.map((balance) {
                                final coin = balance['coin'] ?? '';
                                final amount = balance['total'] ?? 0;

                                return InkWell(
                                  onTap: () {
                                    // Use this coin as base asset for search
                                    setModalState(() {
                                      _symbolSearchController.text = coin;
                                    });
                                    _searchSymbols(coin).then((_) {
                                      setModalState(() {});
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .primary
                                          .withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Text(
                                      '$coin: ${amount.toString()}',
                                      style: TextStyle(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),

                    // Gợi ý phổ biến
                    if (tradingSymbols.isEmpty && !isSearchingSymbols)
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 16, right: 16, top: 8, bottom: 8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Popular Symbols',
                              style: TextStyle(
                                color: ThemeReflector.textColor(
                                  context,
                                  importance: TextImportance.secondary,
                                ),
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: [
                                'AAPL/USD',
                                'GOOG/USD',
                                'AMZN/USD',
                                'TSLA/USD',
                                'MSFT/USD',
                                'NVDA/USD',
                                'META/USD',
                                'BTC/USD',
                                'ETH/USD',
                              ].map((symbol) {
                                return InkWell(
                                  onTap: () {
                                    setModalState(() {
                                      _symbolSearchController.text =
                                          symbol.split('/')[0];
                                    });
                                    _searchSymbols(_symbolSearchController.text)
                                        .then((_) {
                                      setModalState(() {});
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 6),
                                    decoration: BoxDecoration(
                                      color:
                                          ThemeReflector.surfaceColor(context),
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(
                                        color:
                                            ThemeReflector.borderColor(context),
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      symbol,
                                      style: TextStyle(
                                        color: ThemeReflector.textColor(
                                          context,
                                          importance: TextImportance.secondary,
                                        ),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),

                    // Results list
                    Expanded(
                      child: isSearchingSymbols
                          ? const Center(child: CircularProgressIndicator())
                          : tradingSymbols.isEmpty &&
                                  _symbolSearchController.text.isNotEmpty
                              ? Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.search_off,
                                          size: 64,
                                          color: ThemeReflector.iconColor(
                                              context,
                                              opacity: 0.5)),
                                      const SizedBox(height: 16),
                                      Text(
                                        'No symbols found',
                                        style: TextStyle(
                                          color: ThemeReflector.textColor(
                                            context,
                                            importance:
                                                TextImportance.secondary,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Try a different keyword',
                                        style: TextStyle(
                                          color: ThemeReflector.textColor(
                                            context,
                                            importance: TextImportance.tertiary,
                                          ),
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : tradingSymbols.isEmpty &&
                                      _symbolSearchController.text.isEmpty
                                  ? Container() // Hiển thị phần gợi ý trên đầu
                                  : ListView.builder(
                                      controller: scrollController,
                                      itemCount: tradingSymbols.length,
                                      itemBuilder: (context, index) {
                                        final symbol = tradingSymbols[index];
                                        final symbolName = symbol['name'] ?? '';
                                        final nameParts = symbolName.split('/');
                                        final baseAssetName = symbol['from'] ??
                                            (nameParts.isNotEmpty
                                                ? nameParts[0]
                                                : '');
                                        final quoteAssetName = symbol['to'] ??
                                            (nameParts.length > 1
                                                ? nameParts[1]
                                                : '');
                                        final baseImage = symbol['base_image'];
                                        final quoteImage =
                                            symbol['quote_image'];
                                        final price = symbol['last_price'] ??
                                            (symbol['price'] ?? '');

                                        return ListTile(
                                          onTap: () {
                                            Navigator.pop(context);
                                            setState(() {
                                              selectedSymbol = symbolName;
                                              baseAsset = baseAssetName;
                                              quoteAsset = quoteAssetName;
                                              selectedPairId =
                                                  symbol['pair_id'] ??
                                                      symbol['id'] ??
                                                      1;
                                              _baseImageUrl = baseImage;
                                              _symbolSearchController.clear();

                                              // Symbol selection complete

                                              // Fetch detailed information for the selected symbol
                                              _fetchTradingSymbolInfo(
                                                  selectedExchangeId!,
                                                  selectedPairId);
                                            });
                                          },
                                          leading: baseImage != null &&
                                                  baseImage
                                                      .toString()
                                                      .isNotEmpty
                                              ? Container(
                                                  width: 40,
                                                  height: 40,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    image: DecorationImage(
                                                      image: NetworkImage(
                                                          baseImage),
                                                      fit: BoxFit.cover,
                                                    ),
                                                  ),
                                                )
                                              : Container(
                                                  width: 40,
                                                  height: 40,
                                                  decoration: BoxDecoration(
                                                    color: ThemeReflector
                                                        .surfaceColor(context),
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Center(
                                                    child: Text(
                                                      baseAssetName.substring(
                                                          0,
                                                          baseAssetName.length >
                                                                  2
                                                              ? 2
                                                              : baseAssetName
                                                                  .length),
                                                      style: TextStyle(
                                                        color: ThemeReflector
                                                            .textColor(
                                                          context,
                                                          importance:
                                                              TextImportance
                                                                  .primary,
                                                        ),
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                          title: Text(
                                            symbolName,
                                            style: TextStyle(
                                              color: ThemeReflector.textColor(
                                                context,
                                                importance:
                                                    TextImportance.primary,
                                              ),
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          subtitle: Row(
                                            children: [
                                              if (quoteImage != null &&
                                                  quoteImage
                                                      .toString()
                                                      .isNotEmpty)
                                                Container(
                                                  width: 16,
                                                  height: 16,
                                                  margin: const EdgeInsets.only(
                                                      right: 4),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    image: DecorationImage(
                                                      image: NetworkImage(
                                                          quoteImage),
                                                      fit: BoxFit.cover,
                                                    ),
                                                  ),
                                                ),
                                              Text(
                                                price != null &&
                                                        price
                                                            .toString()
                                                            .isNotEmpty
                                                    ? '$quoteAssetName · $price'
                                                    : quoteAssetName,
                                                style: TextStyle(
                                                  color:
                                                      ThemeReflector.textColor(
                                                    context,
                                                    importance:
                                                        TextImportance.tertiary,
                                                  ),
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ],
                                          ),
                                          trailing: Icon(
                                            Icons.check_circle_outline,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary
                                                .withOpacity(0.5),
                                            size: 20,
                                          ),
                                        );
                                      },
                                    ),
                    ),
                  ],
                ),
              );
            },
          );
        });
      },
    );
  }

  void _onSymbolSearchChanged() {
    final searchText = _symbolSearchController.text.trim();
    if (searchText.isNotEmpty && searchText.length >= 2) {
      _searchSymbols(searchText);
    }
  }

  Future<void> _searchSymbols(String query) async {
    if (selectedExchangeId == null) return;

    setState(() {
      isSearchingSymbols = true;
    });

    try {
      final authController =
          Provider.of<AuthController>(context, listen: false);
      final token = authController.token;

      if (token == null) {
        print('Authentication token not found');
        setState(() {
          isSearchingSymbols = false;
        });
        return;
      }

      // Tạo query params
      final Map<String, String> queryParams = {
        'exchange_id': selectedExchangeId.toString(),
        'symbol': query,
      };

      final uri = Uri.parse(
              '${AppConstants.apiBaseUrl}${AppConstants.getExchangePairListEndpoint}')
          .replace(queryParameters: queryParams);

      print('Searching symbols: $uri');

      final response = await http.get(
        uri,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.apiTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['code'] == 200) {
          // Xử lý cấu trúc dữ liệu trả về từ API
          List<Map<String, dynamic>> symbolsList = [];

          // Duyệt qua tất cả các key (ngoại trừ code, message, exchange, transaction_fee)
          data.forEach((key, value) {
            if (key != 'code' &&
                key != 'message' &&
                key != 'exchange' &&
                key != 'transaction_fee' &&
                value is List) {
              // Mỗi key là một danh sách các symbols
              for (var symbol in value) {
                if (symbol is Map<String, dynamic>) {
                  // Thêm các thông tin từ pair nếu có
                  if (symbol['pair'] != null &&
                      symbol['pair'] is Map<String, dynamic>) {
                    symbol['base_image'] = symbol['pair']['base_image'];
                    symbol['quote_image'] = symbol['pair']['quote_image'];
                    symbol['from'] = symbol['pair']['from'];
                    symbol['to'] = symbol['pair']['to'];
                  }
                  symbolsList.add(symbol);
                }
              }
            }
          });

          setState(() {
            tradingSymbols = symbolsList;
            isSearchingSymbols = false;
          });

          print('Found ${tradingSymbols.length} matching symbols');
        } else {
          print('Failed to search symbols: ${data['message']}');
          setState(() {
            isSearchingSymbols = false;
          });
        }
      } else {
        print('Failed to search symbols. Status code: ${response.statusCode}');
        setState(() {
          isSearchingSymbols = false;
        });
      }
    } catch (e) {
      print('Exception in _searchSymbols: $e');
      setState(() {
        isSearchingSymbols = false;
      });
    }
  }
}
