/// Model representing a complete bot configuration
class BotConfig {
  final BotSummary summary;
  final BotEntries entries;
  final BotSubsequentOrders subsequentOrders;
  final BotExits exits;

  const BotConfig({
    required this.summary,
    required this.entries,
    required this.subsequentOrders,
    required this.exits,
  });

  // Create a default bot configuration
  factory BotConfig.defaultConfig() {
    return const BotConfig(
      summary: BotSummary(
        botName: 'Apollo',
        brokerage: 'TradeStation',
        tradingPair: 'AAPL/USD',
        frequency: '4H',
        fundAllocation: 'US\$ 1,000',
        tradeExtendedHour: true,
        strategy: 'Long',
      ),
      entries: BotEntries(
        orderType: 'Market',
        baseOrderLimit: 'US \$500 (50%)',
        baseOrderType: 'Static',
        daisyChain: 'Activated (First)',
        tradingView: ['Alert for Base Order', 'Alert for Extra Order'],
        entryIndicators: ['Bollinger Band', 'RSI (Mandatory)'],
        minIndicatorsRequired: '1 out of 2 Indicators',
      ),
      subsequentOrders: BotSubsequentOrders(
        orderType: 'Market',
        extraOrders: 'US\$ 250 (25%)',
        number: '6',
        minPriceGap: '2.5 %',
      ),
      exits: BotExits(
        takeProfit: '5%',
        stopLoss: '2%', // Add default stopLoss
        tradingView: ['Alert for Base Order', 'Alert for Extra Order'],
        exitIndicators: ['EMA', 'RSI (Mandatory)'],
        minIndicatorsRequired: '2 out of 2 Indicators',
        stopAfterCurrentDeal: true,
        autoCloseAtEndOfDay: true,
      ),
    );
  }

  // Convert from Map to BotConfig
  factory BotConfig.fromMap(Map<String, dynamic> map) {
    return BotConfig(
      summary: BotSummary.fromMap(map['summary']),
      entries: BotEntries.fromMap(map['entries']),
      subsequentOrders: BotSubsequentOrders.fromMap(map['subsequentOrders']),
      exits: BotExits.fromMap(map['exits']),
    );
  }

  // Convert to Map from BotConfig
  Map<String, dynamic> toMap() {
    return {
      'summary': summary.toMap(),
      'entries': entries.toMap(),
      'subsequentOrders': subsequentOrders.toMap(),
      'exits': exits.toMap(),
    };
  }
}

/// Bot summary information
class BotSummary {
  final String botName;
  final String brokerage;
  final String tradingPair;
  final String frequency;
  final String fundAllocation;
  final bool tradeExtendedHour;
  final String strategy;

  const BotSummary({
    required this.botName,
    required this.brokerage,
    required this.tradingPair,
    required this.frequency,
    required this.fundAllocation,
    required this.tradeExtendedHour,
    required this.strategy,
  });

  factory BotSummary.fromMap(Map<String, dynamic> map) {
    return BotSummary(
      botName: map['botName'] ?? '',
      brokerage: map['brokerage'] ?? '',
      tradingPair: map['tradingPair'] ?? '',
      frequency: map['frequency'] ?? '',
      fundAllocation: map['fundAllocation'] ?? '',
      tradeExtendedHour: map['tradeExtendedHour'] ?? false,
      strategy: map['strategy'] ?? 'Long',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'botName': botName,
      'brokerage': brokerage,
      'tradingPair': tradingPair,
      'frequency': frequency,
      'fundAllocation': fundAllocation,
      'tradeExtendedHour': tradeExtendedHour,
    };
  }
}

/// Bot entry settings
class BotEntries {
  final String orderType;
  final String baseOrderLimit;
  final String baseOrderType;
  final String daisyChain;
  final List<String> tradingView;
  final List<String> entryIndicators;
  final String minIndicatorsRequired;
  final String entryType; // Grid entry type: Immediate/Indicator/Range
  final String? rangePercentage; // % change for Range entry type
  final String? rangePeriods; // Periods for Range entry type
  final bool? isRangeLesser; // Direction for Range entry type

  // Price bot specific fields
  final String? priceEntry; // Buy when price is less than or equal to

  // Sell bot specific fields
  final String? averageEntryPrice; // Average entry price for sell bot

  // DCA bot specific fields
  final String? dcaEntryType; // time or price
  final String? dcaValue; // daily, weekly, monthly
  final String? dcaValue2; // interval number
  final String? dcaValue3; // additional parameter

  const BotEntries({
    required this.orderType,
    required this.baseOrderLimit,
    required this.baseOrderType,
    required this.daisyChain,
    required this.tradingView,
    required this.entryIndicators,
    required this.minIndicatorsRequired,
    this.entryType = 'Immediate', // Default to Immediate
    this.rangePercentage,
    this.rangePeriods,
    this.isRangeLesser,
    // Price bot fields
    this.priceEntry,
    // Sell bot fields
    this.averageEntryPrice,
    // DCA bot fields
    this.dcaEntryType,
    this.dcaValue,
    this.dcaValue2,
    this.dcaValue3,
  });

  factory BotEntries.fromMap(Map<String, dynamic> map) {
    return BotEntries(
      orderType: map['orderType'] ?? '',
      baseOrderLimit: map['baseOrderLimit'] ?? '',
      baseOrderType: map['baseOrderType'] ?? '',
      daisyChain: map['daisyChain'] ?? '',
      tradingView: List<String>.from(map['tradingView'] ?? []),
      entryIndicators: List<String>.from(map['entryIndicators'] ?? []),
      minIndicatorsRequired: map['minIndicatorsRequired'] ?? '',
      entryType: map['entryType'] ?? 'Immediate',
      rangePercentage: map['rangePercentage'],
      rangePeriods: map['rangePeriods'],
      isRangeLesser: map['isRangeLesser'],
      // Price bot fields
      priceEntry: map['priceEntry'],
      // Sell bot fields
      averageEntryPrice: map['averageEntryPrice'],
      // DCA bot fields
      dcaEntryType: map['dcaEntryType'],
      dcaValue: map['dcaValue'],
      dcaValue2: map['dcaValue2'],
      dcaValue3: map['dcaValue3'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'orderType': orderType,
      'baseOrderLimit': baseOrderLimit,
      'baseOrderType': baseOrderType,
      'daisyChain': daisyChain,
      'tradingView': tradingView,
      'entryIndicators': entryIndicators,
      'minIndicatorsRequired': minIndicatorsRequired,
      'entryType': entryType,
      'rangePercentage': rangePercentage,
      'rangePeriods': rangePeriods,
      'isRangeLesser': isRangeLesser,
      // Price bot fields
      'priceEntry': priceEntry,
      // Sell bot fields
      'averageEntryPrice': averageEntryPrice,
      // DCA bot fields
      'dcaEntryType': dcaEntryType,
      'dcaValue': dcaValue,
      'dcaValue2': dcaValue2,
      'dcaValue3': dcaValue3,
    };
  }
}

/// Bot subsequent orders settings
class BotSubsequentOrders {
  final String orderType;
  final String extraOrders;
  final String number;
  final String minPriceGap;
  // Grid-specific parameters
  final String? gridUpperRange; // Upper Range Sell Price Limit
  final String? gridLowerRange; // Lower Range Buy Price Limit
  final String?
      gridOrderNum; // Number of Orders Between Mid Price and Range Limit
  final String? gridOrderVol; // Per Order Amount
  // Trade parameter inputs
  final String? fundAllocation; // Fund allocation from user input
  final String? orderTypeEntry; // Entry order type (market/limit)
  final String? orderTypeExit; // Exit order type (market/limit)
  final String? baseOrderType; // Base order type (static/dynamic)
  final bool? extendedTradingHour; // Extended trading hours
  final String? tradingFrequency; // Trading frequency

  const BotSubsequentOrders({
    required this.orderType,
    required this.extraOrders,
    required this.number,
    required this.minPriceGap,
    this.gridUpperRange,
    this.gridLowerRange,
    this.gridOrderNum,
    this.gridOrderVol,
    this.fundAllocation,
    this.orderTypeEntry,
    this.orderTypeExit,
    this.baseOrderType,
    this.extendedTradingHour,
    this.tradingFrequency,
  });

  factory BotSubsequentOrders.fromMap(Map<String, dynamic> map) {
    return BotSubsequentOrders(
      orderType: map['orderType'] ?? '',
      extraOrders: map['extraOrders'] ?? '',
      number: map['number'] ?? '',
      minPriceGap: map['minPriceGap'] ?? '',
      gridUpperRange: map['gridUpperRange'],
      gridLowerRange: map['gridLowerRange'],
      gridOrderNum: map['gridOrderNum'],
      gridOrderVol: map['gridOrderVol'],
      fundAllocation: map['fundAllocation'],
      orderTypeEntry: map['orderTypeEntry'],
      orderTypeExit: map['orderTypeExit'],
      baseOrderType: map['baseOrderType'],
      extendedTradingHour: map['extendedTradingHour'],
      tradingFrequency: map['tradingFrequency'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'orderType': orderType,
      'extraOrders': extraOrders,
      'number': number,
      'minPriceGap': minPriceGap,
      'gridUpperRange': gridUpperRange,
      'gridLowerRange': gridLowerRange,
      'gridOrderNum': gridOrderNum,
      'gridOrderVol': gridOrderVol,
      'fundAllocation': fundAllocation,
      'orderTypeEntry': orderTypeEntry,
      'orderTypeExit': orderTypeExit,
      'baseOrderType': baseOrderType,
      'extendedTradingHour': extendedTradingHour,
      'tradingFrequency': tradingFrequency,
    };
  }
}

/// Bot exit settings
class BotExits {
  final String takeProfit;
  final String? stopLoss; // Add stopLoss field
  final List<String> tradingView;
  final List<String> exitIndicators;
  final String minIndicatorsRequired;
  final bool stopAfterCurrentDeal;
  final bool autoCloseAtEndOfDay;

  // Price bot specific field
  final String? priceExit; // Sell when price is more than or equal to

  const BotExits({
    required this.takeProfit,
    this.stopLoss, // Add stopLoss parameter
    required this.tradingView,
    required this.exitIndicators,
    required this.minIndicatorsRequired,
    required this.stopAfterCurrentDeal,
    required this.autoCloseAtEndOfDay,
    // Price bot field
    this.priceExit,
  });

  factory BotExits.fromMap(Map<String, dynamic> map) {
    return BotExits(
      takeProfit: map['takeProfit'] ?? '',
      stopLoss: map['stopLoss'], // Add stopLoss mapping
      tradingView: List<String>.from(map['tradingView'] ?? []),
      exitIndicators: List<String>.from(map['exitIndicators'] ?? []),
      minIndicatorsRequired: map['minIndicatorsRequired'] ?? '',
      stopAfterCurrentDeal: map['stopAfterCurrentDeal'] ?? false,
      autoCloseAtEndOfDay: map['autoCloseAtEndOfDay'] ?? false,
      // Price bot field
      priceExit: map['priceExit'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'takeProfit': takeProfit,
      'stopLoss': stopLoss, // Add stopLoss to map
      'tradingView': tradingView,
      'exitIndicators': exitIndicators,
      'minIndicatorsRequired': minIndicatorsRequired,
      'stopAfterCurrentDeal': stopAfterCurrentDeal,
      'autoCloseAtEndOfDay': autoCloseAtEndOfDay,
      // Price bot field
      'priceExit': priceExit,
    };
  }
}
