import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:stockhero/models/bot_config.dart';
import 'package:stockhero/services/bot_payload_builders/bot_payload_builder_factory.dart';
import 'package:stockhero/services/bot_service.dart';
import 'package:stockhero/services/subscription_validation_service.dart';
import 'package:stockhero/shared/controllers/auth_controller.dart';
import 'package:stockhero/shared/providers/bot_config_provider.dart';
import 'package:stockhero/shared/utils/theme_reflector.dart';
import 'package:stockhero/shared/utils/theme_utils.dart';
import "package:stockhero/utils/constants.dart";

class DeployBotScreen extends StatefulWidget {
  const DeployBotScreen({Key? key}) : super(key: key);

  @override
  State<DeployBotScreen> createState() => _DeployBotScreenState();
}

class _DeployBotScreenState extends State<DeployBotScreen> {
  bool _isDeploying = false;
  bool _isEditMode = false;
  String? botType;
  final BotService _botService = BotService();

  // Bot settings for the API
  final Map<String, dynamic> botSettings = {
    'name': 'Super Algo Bot',
    'symbol': 'NIFTY',
    'strategy': 'Momentum Trading',
    'tradingParameters': {
      'entryTime': '9:15 AM',
      'exitTime': '3:15 PM',
      'targetPercentage': 2.5,
      'stopLossPercentage': 1.0,
      'trailingStopLoss': true,
      'maxLoss': 5000,
      'capital': 100000,
    },
    'entrySettings': {
      'enableDaisyChain': true,
      'isFirstInDaisyChain': false,
      'integrateTradingView': true,
      'indicators': ['Bollinger Band 20, 2.0', 'RSI - 14, 30, 25, true'],
      'minIndicators': 1.0,
    },
    'exitSettings': {
      'enableDaisyChain': true,
      'isFirstInDaisyChain': false,
      'integrateTradingView': true,
      'alerts': [
        'Alert for Base Order',
        'Alert for Extra Order',
      ],
    },
  };

  @override
  void initState() {
    super.initState();
    print("DeployBotScreen initState called");

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Check if we're in edit mode by examining route arguments
      final args =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      print("Route arguments: $args");

      if (args != null && args.containsKey('isEditing')) {
        final isEditing = args['isEditing'] == true;
        print("isEditing from arguments: $isEditing");

        setState(() {
          _isEditMode = isEditing;
          botType = args['botType']?.toString();
        });
        print("_isEditMode set to: $_isEditMode");
        print("botType set to: $botType");

        if (_isEditMode && args.containsKey('botId')) {
          // If editing, we should load the bot data for the given bot ID
          final botId = args['botId'];
          print("botId from arguments: $botId");

          if (botId != null) {
            _loadBotData(botId);
          }
        }
      } else {
        // If no arguments or no isEditing flag, we're in create mode
        setState(() {
          _isEditMode = false;
        });
        print("No edit arguments, set to create mode (_isEditMode=false)");
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Scaffold(
      backgroundColor: ThemeReflector.screenBackground(context),
      appBar: AppBar(
        backgroundColor: ThemeReflector.surfaceColor(context),
        elevation: 0,
        title: Text(
          _isEditMode ? 'Edit Bot' : 'Create Bot',
          style: TextStyle(
            color: ThemeReflector.textColor(
              context,
              importance: TextImportance.primary,
            ),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: ThemeReflector.iconColor(context),
            size: 24,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.close,
              color: ThemeReflector.iconColor(context),
              size: 24,
            ),
            onPressed: () => Navigator.pushNamedAndRemoveUntil(
              context,
              AppConstants.dashboardRoute,
              (route) => false,
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Header with image and title
                      _buildHeader(theme, colorScheme, textTheme),

                      // Bot configuration sections
                      _buildSummarySection(theme, colorScheme, textTheme),
                      _buildEntriesSection(theme, colorScheme, textTheme),
                      _buildSubsequentOrdersSection(
                          theme, colorScheme, textTheme),
                      _buildExitsSection(theme, colorScheme, textTheme),
                    ],
                  ),
                ),
              ),

              // Bottom buttons
              Container(
                color: ThemeReflector.surfaceColor(context),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Column(
                  children: [
                    // Backtest button
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: OutlinedButton(
                        onPressed: _isDeploying ? null : _handleBacktest,
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: colorScheme.primary),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Backtest',
                          style: TextStyle(
                            color: colorScheme.primary,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Deploy/Update button
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: ElevatedButton(
                        onPressed: _isDeploying ? null : _handleDeployBot,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: _isDeploying
                            ? SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Theme.of(context).colorScheme.onPrimary),
                                ),
                              )
                            : Text(
                                _isEditMode ? 'Update Bot' : 'Deploy Bot',
                                style: TextStyle(
                                  color:
                                      Theme.of(context).colorScheme.onPrimary,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],
                ),
              ),
            ],
          ),

          // Full screen loading overlay
          if (_isDeploying)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: Center(
                child: Card(
                  elevation: 4,
                  color: ThemeReflector.surfaceColor(context),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _isEditMode ? 'Updating Bot...' : 'Deploying Bot...',
                          style: TextStyle(
                            color: ThemeReflector.textColor(
                              context,
                              importance: TextImportance.primary,
                            ),
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeader(
      ThemeData theme, ColorScheme colorScheme, TextTheme textTheme) {
    // Choose image based on bot type
    String imagePath;
    switch (botType?.toLowerCase()) {
      case 'grid':
        imagePath = 'assets/images/grid_summary.png';
        break;
      case 'momentum':
        imagePath = 'assets/images/momentum_summary.png';
        break;
      default:
        imagePath = 'assets/images/trading_deploy.png';
        break;
    }

    return Container(
      color: ThemeReflector.screenBackground(context),
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        children: [
          Image.asset(
            imagePath,
            width: 120,
            height: 120,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 48,
                      color: colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Summary",
                      style: TextStyle(
                        color: ThemeReflector.textColor(
                          context,
                          importance: TextImportance.primary,
                        ),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                _isEditMode
                    ? "Update: Ready to Save Changes"
                    : "Create: Ready to Deploy",
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.secondary,
                  ),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Progress bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection(
      ThemeData theme, ColorScheme colorScheme, TextTheme textTheme) {
    final botConfig = Provider.of<BotConfigProvider>(context).botConfig;
    final summary = botConfig.summary;

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(20),
      color: ThemeReflector.surfaceColor(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Summary",
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.primary,
              ),
              fontSize: 20,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16),
          // Bot name
          _buildSectionRow(
            label: 'Bot name',
            value: summary.botName,
            hasBorder: true,
            theme: theme,
            textTheme: textTheme,
          ),

          // Brokerage
          _buildSectionRow(
            label: 'Brokerage',
            value: summary.brokerage,
            hasBorder: true,
            theme: theme,
            textTheme: textTheme,
          ),

          // Trading Pair
          _buildSectionRow(
            label: 'Trading Pair',
            value: summary.tradingPair,
            hasBorder: true,
            theme: theme,
            textTheme: textTheme,
            iconBuilder: () => Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: colorScheme.primary,
                borderRadius: BorderRadius.circular(24),
              ),
              margin: const EdgeInsets.only(right: 4),
              padding: const EdgeInsets.all(6),
              child: const Icon(
                Icons.currency_bitcoin,
                size: 14,
                color: Colors.white,
              ),
            ),
          ),

          // Frequency
          _buildSectionRow(
            label: 'Frequency',
            value: summary.frequency,
            hasBorder: true,
            theme: theme,
            textTheme: textTheme,
          ),

          // Fund Allocation
          _buildSectionRow(
            label: 'Fund Allocation',
            value: summary.fundAllocation,
            hasBorder: false,
            theme: theme,
            textTheme: textTheme,
          ),

          const SizedBox(height: 16),

          // Bot type specific info
          if (botType?.toLowerCase() == 'grid')
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.grid_on,
                    color: Colors.green,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text(
                    "Grid Trading Strategy",
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            )
          else if (botType?.toLowerCase() == 'momentum')
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.trending_up,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    "Momentum Trading Strategy",
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

          // Extended hours flag
          if (summary.tradeExtendedHour)
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: Text(
                "Trade Extended hour Activated",
                style: textTheme.bodyMedium?.copyWith(
                  color: textTheme.bodyMedium?.color?.withOpacity(0.7),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSectionRow({
    required String label,
    required String value,
    bool hasBorder = true,
    Widget Function()? iconBuilder,
    required ThemeData theme,
    required TextTheme textTheme,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        border: hasBorder
            ? Border(
                bottom: BorderSide(
                  color: ThemeReflector.dividerColor(context),
                  width: 1,
                ),
              )
            : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.secondary,
              ),
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          Flexible(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (iconBuilder != null) iconBuilder(),
                Flexible(
                  child: Text(
                    value,
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.primary,
                      ),
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEntriesSection(
      ThemeData theme, ColorScheme colorScheme, TextTheme textTheme) {
    final botConfig = Provider.of<BotConfigProvider>(context).botConfig;
    final entries = botConfig.entries;

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(20),
      color: ThemeReflector.surfaceColor(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Entries",
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.primary,
              ),
              fontSize: 20,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16),

          // Grid-specific entries
          if (botType?.toLowerCase() == 'grid') ...[
            _buildSectionRow(
              label: 'Upper Range (Sell Limit)',
              value: _extractPercentageFromString(
                  entries.baseOrderLimit), // Extract % từ "US $500 (50%)"
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
            _buildSectionRow(
              label: 'Lower Range (Buy Limit)',
              value: botConfig.subsequentOrders.minPriceGap, // "2.5%"
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
            _buildSectionRow(
              label: 'Number of Orders',
              value: botConfig.subsequentOrders.number, // "6"
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
            _buildSectionRow(
              label: 'Per Order Amount',
              value: _extractDollarFromString(botConfig.subsequentOrders
                  .extraOrders), // Extract $ từ "US$ 250 (25%)"
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
          ] else if (botType?.toLowerCase() == 'momentum') ...[
            _buildSectionRow(
              label: 'Base Order Trigger',
              value:
                  'Price increases by ${_extractPercentageFromString(entries.baseOrderLimit)}', // Extract % từ "US $500 (50%)"
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
            _buildSectionRow(
              label: 'Extra Order Trigger',
              value:
                  'Price continues to increase by ${_extractPercentageFromString(botConfig.subsequentOrders.extraOrders)}', // Extract % từ "US$ 250 (25%)"
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
            _buildSectionRow(
              label: 'Max Extra Orders',
              value: '1 order only', // Fixed theo documentation
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
          ] else ...[
            // Standard bot entries
            _buildSectionRow(
              label: 'Order Type',
              value: entries.orderType,
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
            _buildSectionRow(
              label: 'Base Order Limit',
              value: entries.baseOrderLimit,
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
            _buildSectionRow(
              label: 'Base Order Type',
              value: entries.baseOrderType,
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
          ],

          // Daisy Chain (common for all bot types)
          _buildSectionRow(
            label: 'Daisy Chain',
            value: entries.daisyChain,
            hasBorder: false,
            theme: theme,
            textTheme: textTheme,
          ),

          const SizedBox(height: 16),

          // Trading View
          if (entries.tradingView.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: theme.dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Trading View",
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.secondary,
                      ),
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      for (var item in entries.tradingView)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 5.0),
                          child: Text(
                            item,
                            style: TextStyle(
                              color: ThemeReflector.textColor(
                                context,
                                importance: TextImportance.primary,
                              ),
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),

          // Entry Indicator
          if (entries.entryIndicators.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: theme.dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Entry Indicator",
                    style: textTheme.bodyMedium?.copyWith(
                      color: textTheme.bodyMedium?.color?.withOpacity(0.7),
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      for (var item in entries.entryIndicators)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 5.0),
                          child: Text(
                            item,
                            style: textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),

          // Min Indicator required
          _buildSectionRow(
            label: 'Min indicator required for entry',
            value: entries.minIndicatorsRequired,
            hasBorder: false,
            theme: theme,
            textTheme: textTheme,
          ),
        ],
      ),
    );
  }

  Widget _buildSubsequentOrdersSection(
      ThemeData theme, ColorScheme colorScheme, TextTheme textTheme) {
    final botConfig = Provider.of<BotConfigProvider>(context).botConfig;
    final subsequentOrders = botConfig.subsequentOrders;

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(20),
      color: ThemeReflector.surfaceColor(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Subsequent Orders",
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.primary,
              ),
              fontSize: 20,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16),

          // Order Type
          _buildSectionRow(
            label: 'Order Type',
            value: subsequentOrders.orderType,
            hasBorder: true,
            theme: theme,
            textTheme: textTheme,
          ),

          // Extra Orders
          _buildSectionRow(
            label: 'Extra Orders',
            value: subsequentOrders.extraOrders,
            hasBorder: true,
            theme: theme,
            textTheme: textTheme,
          ),

          // Number
          _buildSectionRow(
            label: 'Number',
            value: subsequentOrders.number,
            hasBorder: true,
            theme: theme,
            textTheme: textTheme,
          ),

          // Min price gap
          _buildSectionRow(
            label: 'Min price gap between extra order',
            value: subsequentOrders.minPriceGap,
            hasBorder: false,
            theme: theme,
            textTheme: textTheme,
          ),
        ],
      ),
    );
  }

  Widget _buildExitsSection(
      ThemeData theme, ColorScheme colorScheme, TextTheme textTheme) {
    final botConfig = Provider.of<BotConfigProvider>(context).botConfig;
    final exits = botConfig.exits;

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(20),
      color: ThemeReflector.surfaceColor(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Exits",
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.primary,
              ),
              fontSize: 20,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16),

          // Grid-specific exits
          if (botType?.toLowerCase() == 'grid') ...[
            _buildSectionRow(
              label: 'Wait Time',
              value:
                  '${botConfig.exits.takeProfit} minutes', // Có thể cần field riêng cho wait time
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
            _buildSectionRow(
              label: 'Outer Range Limit',
              value: botConfig.exits.takeProfit, // Actual value từ user input
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
            _buildSectionRow(
              label: 'Exit Strategy',
              value: 'Cancel & Restart', // Fixed theo documentation
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
          ] else if (botType?.toLowerCase() == 'momentum') ...[
            _buildSectionRow(
              label: 'Take Profit',
              value: exits.takeProfit,
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
            _buildSectionRow(
              label: 'Stop Loss',
              value: '${_parseStopLoss(exits.takeProfit).toStringAsFixed(1)}%',
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
            _buildSectionRow(
              label: 'Profit/Loss Basis',
              value: 'Average Purchase Price',
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
          ] else ...[
            // Standard bot exits
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                "Absolute",
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.primary,
                  ),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            _buildSectionRow(
              label: 'Take Profit',
              value: exits.takeProfit,
              hasBorder: true,
              theme: theme,
              textTheme: textTheme,
            ),
          ],

          // Trading View
          if (exits.tradingView.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: theme.dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Trading View",
                    style: textTheme.bodyMedium?.copyWith(
                      color: textTheme.bodyMedium?.color?.withOpacity(0.7),
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      for (var item in exits.tradingView)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 5.0),
                          child: Text(
                            item,
                            style: textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),

          // Exit Indicator
          if (exits.exitIndicators.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: theme.dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Exit Indicator",
                    style: textTheme.bodyMedium?.copyWith(
                      color: textTheme.bodyMedium?.color?.withOpacity(0.7),
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      for (var item in exits.exitIndicators)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 5.0),
                          child: Text(
                            item,
                            style: textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),

          // Min Indicator required
          _buildSectionRow(
            label: 'Min indicator required for exit',
            value: exits.minIndicatorsRequired,
            hasBorder: true,
            theme: theme,
            textTheme: textTheme,
          ),

          const SizedBox(height: 20),

          // Additional settings
          if (exits.stopAfterCurrentDeal)
            Text(
              "Stop this bot after the current deal ends",
              style: textTheme.bodyMedium?.copyWith(
                color: textTheme.bodyMedium?.color?.withOpacity(0.7),
              ),
            ),

          const SizedBox(height: 3),

          if (exits.autoCloseAtEndOfDay)
            Text(
              "Auto-close open positions at the end of the trading day",
              style: textTheme.bodyMedium?.copyWith(
                color: textTheme.bodyMedium?.color?.withOpacity(0.7),
              ),
            ),
        ],
      ),
    );
  }

  // Method to create the bot
  void _createBot() {
    _handleDeployBot();
  }

  void _handleBacktest() {
    // Get data from route args (if available)
    final Map<String, dynamic>? routeArgs =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;

    // Default values if arguments are missing
    final String strategy = routeArgs?['strategy'] ?? 'Long';
    final int exchangePairId = routeArgs?['pairId'] ?? 2;
    final int exchangeId = routeArgs?['exchangeId'] ?? 0;

    // Navigate to backtest screen with parameters
    Navigator.pushNamed(
      context,
      AppConstants.backtestRoute,
      arguments: {
        'strategy': strategy,
        'pairId': exchangePairId,
        'exchangeId': exchangeId,
        'isEditing': _isEditMode,
        'botId': _isEditMode ? _getBotIdFromArguments() : null,
      },
    );
  }

  void _handleDeployBot() async {
    setState(() {
      _isDeploying = true;
    });

    try {
      // Get route arguments
      final Map<String, dynamic>? routeArgs =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;

      // Get the bot configuration from the provider
      final botConfigProvider =
          Provider.of<BotConfigProvider>(context, listen: false);
      final configFromProvider = botConfigProvider.botConfig;

      // Validate subscription plan limits before proceeding
      final authController =
          Provider.of<AuthController>(context, listen: false);

      // Validate extra orders
      double extraOrderPercentage = _parsePercentageValue(
          configFromProvider.subsequentOrders.extraOrders);
      final extraOrderValidation =
          SubscriptionValidationService.validateExtraOrders(
              authController, extraOrderPercentage);

      if (!extraOrderValidation.isValid) {
        setState(() {
          _isDeploying = false;
        });
        SubscriptionValidationService.showUpgradeDialog(
          context,
          extraOrderValidation.message!,
          extraOrderValidation.requiredTier!,
        );
        return;
      }

      // Validate number of indicators
      int totalIndicators = configFromProvider.entries.entryIndicators.length +
          configFromProvider.exits.exitIndicators.length;
      final indicatorValidation =
          SubscriptionValidationService.validateIndicators(
              authController, totalIndicators);

      if (!indicatorValidation.isValid) {
        setState(() {
          _isDeploying = false;
        });
        SubscriptionValidationService.showUpgradeDialog(
          context,
          indicatorValidation.message!,
          indicatorValidation.requiredTier!,
        );
        return;
      }

      // Validate extended hours trading if enabled
      if (configFromProvider.summary.tradeExtendedHour) {
        final extendedHoursValidation =
            SubscriptionValidationService.validateExtendedHours(authController);

        if (!extendedHoursValidation.isValid) {
          setState(() {
            _isDeploying = false;
          });
          SubscriptionValidationService.showUpgradeDialog(
            context,
            extendedHoursValidation.message!,
            extendedHoursValidation.requiredTier!,
          );
          return;
        }
      }

      // Validate active bot count (only for new bots, not edits)
      if (!_isEditMode) {
        // TODO: Get current active bot count from API
        // For now, we'll skip this validation as it requires an API call
        // In a real implementation, you would call the bot service to get the count
      }

      // Bot parameter validation complete

      // Pre-process and validate critical values
      double initialFund =
          _parseInitialFund(configFromProvider.summary.fundAllocation);

      // Remove local fund validation - let backend handle this

      double baseOrderPercentage =
          _parsePercentageValue(configFromProvider.entries.baseOrderLimit);
      if (baseOrderPercentage > 100) baseOrderPercentage = 100;
      // Ensure base order percentage is never 0 for grid bot
      if (baseOrderPercentage <= 0) baseOrderPercentage = 50; // Default 50%

      // extraOrderPercentage already declared above for validation
      if (extraOrderPercentage > 100) extraOrderPercentage = 100;
      // Ensure extra order percentage is never 0 for grid bot
      if (extraOrderPercentage <= 0) extraOrderPercentage = 25; // Default 25%

      double minPriceGap = _parsePercentageValue(
          configFromProvider.subsequentOrders.minPriceGap);
      if (minPriceGap > 100) minPriceGap = 100;

      double takeProfit =
          _parsePercentageValue(configFromProvider.exits.takeProfit);
      if (takeProfit > 100) takeProfit = 100;

      // Parse stop loss from BotConfig.exits.stopLoss instead of takeProfit
      double stopLoss = 0.0;
      if (configFromProvider.exits.stopLoss != null &&
          configFromProvider.exits.stopLoss!.isNotEmpty) {
        stopLoss = _parsePercentageValue(configFromProvider.exits.stopLoss!);
        if (stopLoss > 100) stopLoss = 100;
      }

      print(
          "Parsed values - Base Order: $baseOrderPercentage%, Extra Orders: $extraOrderPercentage%, Min Price Gap: $minPriceGap%");
      print("Parsed values - Take Profit: $takeProfit%, Stop Loss: $stopLoss%");

      // Get data from route args (if available) - use existing routeArgs

      // Extract the final strategy, trading pair info and exchange_pair_id from route args
      String strategy =
          routeArgs?['strategy'] ?? "Long"; // Get from route args or fallback
      int exchangePairId = 2;
      int exchangeId = 0;

      if (routeArgs != null) {
        print("Route arguments received: $routeArgs");

        // Use exchange_pair_id from route args if available
        if (routeArgs.containsKey('pairId')) {
          exchangePairId = routeArgs['pairId'] ?? 2;
        }

        // Use exchange_id from route args if available
        if (routeArgs.containsKey('exchangeId')) {
          exchangeId = routeArgs['exchangeId'] ?? 0;
        }

        // Route args processed

        // Also check if we have base and quote assets
        if (routeArgs.containsKey('baseAsset') &&
            routeArgs.containsKey('quoteAsset')) {
          String baseAsset = routeArgs['baseAsset'];
          String quoteAsset = routeArgs['quoteAsset'];
          print("Base Asset from route args: $baseAsset");
          print("Quote Asset from route args: $quoteAsset");
        }
      } else {
        print("WARNING: No route arguments received!");
      }

      // Balance checking removed - handled by backend

      // Extract values from UI steps for the API call using new architecture
      final Map<String, dynamic> apiParams = _buildApiParamsWithFactory(
        botConfigProvider,
        exchangePairId,
        exchangeId,
        strategy,
      );

      Map<String, dynamic> result;

      // Call the appropriate API method based on mode
      if (_isEditMode) {
        final botId = _getBotIdFromArguments();
        if (botId == null) {
          throw Exception('Bot ID is required for update');
        }
        print("Updating bot with ID: $botId");
        print("API Parameters: $apiParams");
        result = await _botService.updateBot(botId, apiParams);
      } else {
        // Creating new bot

        result = await _botService.deployBot(apiParams);
      }

      setState(() {
        _isDeploying = false;
      });

      // Process response

      if (result.containsKey('error')) {
        print('Error found in response: ${result['error']}');
        _showErrorSnackBar(result['error'].toString());
        return;
      }

      // Check for success response (code 200 or 201)
      final responseCode = result['code'];
      if (responseCode == 200 || responseCode == 201) {
        // Validate that bot was actually created
        bool botActuallyCreated = false;

        // Check both 'data' and 'bot' fields (server uses 'bot' field)
        Map? botData;
        if (result.containsKey('bot') && result['bot'] != null) {
          botData = result['bot'] as Map;
          print('Bot data found in "bot" field: $botData');
        } else if (result.containsKey('data') && result['data'] != null) {
          botData = result['data'] as Map;
          print('Bot data found in "data" field: $botData');
        }

        if (botData != null) {
          final createdBotId = botData['id']?.toString();
          botActuallyCreated = createdBotId != null && createdBotId.isNotEmpty;
          print('Created bot ID: $createdBotId');
        }

        if (botActuallyCreated) {
          final botName = configFromProvider.summary.botName;
          final String successMessage = _isEditMode
              ? "Bot '$botName' has been successfully updated!"
              : "Bot '$botName' has been successfully deployed!";

          print('Bot deployment successful: $successMessage');
          _showSuccessDialog(successMessage);
        } else {
          print('Bot creation failed - no bot ID returned from server');
          _showErrorSnackBar(
              "Bot creation failed - no bot ID returned from server");
        }
      } else {
        // Handle error response
        final errorMessage = result['message'] ??
            result['error'] ??
            "Unknown error occurred (Code: $responseCode)";

        print('Bot deployment failed: $errorMessage');
        _showErrorSnackBar(errorMessage);
      }
    } catch (e) {
      setState(() {
        _isDeploying = false;
      });
      _showErrorSnackBar(e.toString());
    }
  }

  // Helper methods for parsing various fields
  double _parseInitialFund(String fundAllocation) {
    // Parse "US$ 1,000" to 1000.0
    final regex = RegExp(r'[\d,]+\.?\d*');
    final match = regex.firstMatch(fundAllocation);
    if (match != null) {
      final numStr = match.group(0)?.replaceAll(',', '') ?? '0';
      return double.tryParse(numStr) ?? 0.0;
    }
    return 0.0;
  }

  double _parsePercentageValue(String percentageStr) {
    // Parse strings like:
    // "US $500 (50%)" or "US$ 250 (25%)" to just get the percentage value

    if (percentageStr.contains('(') && percentageStr.contains(')')) {
      // Extract just the percentage part from formats like "US $500 (50%)"
      final regex = RegExp(r'\((\d+\.?\d*)%\)');
      final match = regex.firstMatch(percentageStr);
      if (match != null && match.groupCount >= 1) {
        return double.tryParse(match.group(1) ?? '0') ?? 0.0;
      }
    }

    // Fallback to the old method for other formats like "2.5%"
    final regex = RegExp(r'[\d,]+\.?\d*');
    final match = regex.firstMatch(percentageStr);
    if (match != null) {
      final numStr = match.group(0)?.replaceAll(',', '') ?? '0';
      return double.tryParse(numStr) ?? 0.0;
    }
    return 0.0;
  }

  int _parseFrequency(String frequency) {
    // Convert frequency like "4H" to minutes
    if (frequency.contains('H')) {
      final hours = int.tryParse(frequency.replaceAll('H', '').trim()) ?? 1;
      return hours * 60; // Convert hours to minutes
    } else if (frequency.contains('D')) {
      final days = int.tryParse(frequency.replaceAll('D', '').trim()) ?? 1;
      return days * 24 * 60; // Convert days to minutes
    } else if (frequency.contains('M')) {
      return int.tryParse(frequency.replaceAll('M', '').trim()) ?? 1;
    }
    return 15; // Default to 15 minutes
  }

  int _parseMinIndicators(String minIndicatorsStr) {
    // Parse "1 out of 2 Indicators" to 1
    final regex = RegExp(r'\d+');
    final matches = regex.allMatches(minIndicatorsStr);
    if (matches.isNotEmpty) {
      return int.tryParse(matches.first.group(0) ?? '1') ?? 1;
    }
    return 1;
  }

  double _parseStopLoss(String stopLossStr) {
    // Similar to _parsePercentageValue but with a default of 5%
    final percentage = _parsePercentageValue(stopLossStr);
    return percentage > 0 ? percentage : 5.0;
  }

  int _parseNumberValue(String numberStr) {
    // Parse number from strings like "5 orders" to 5
    final regex = RegExp(r'\d+');
    final match = regex.firstMatch(numberStr);
    if (match != null) {
      return int.tryParse(match.group(0) ?? '1') ?? 1;
    }
    return 1;
  }

  String _extractPercentageFromString(String input) {
    // Extract percentage from strings like "US $500 (50%)" to "50%"
    final regex = RegExp(r'\((\d+(?:\.\d+)?%)\)');
    final match = regex.firstMatch(input);
    if (match != null) {
      return match.group(1) ?? input;
    }
    return input;
  }

  String _extractDollarFromString(String input) {
    // Extract dollar amount from strings like "US$ 250 (25%)" to "US$ 250"
    final regex = RegExp(r'(US\$?\s*[\d,]+(?:\.\d+)?)');
    final match = regex.firstMatch(input);
    if (match != null) {
      return match.group(1) ?? input;
    }
    return input;
  }

  // Helper to get bot ID from route arguments
  int? _getBotIdFromArguments() {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    return args?['botId'] as int?;
  }

  // Show success dialog
  void _showSuccessDialog(String message) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: ThemeReflector.surfaceColor(context),
          title: Text(
            "Success",
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.primary,
              ),
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            message,
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.secondary,
              ),
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog

                // Navigate to dashboard screen after successful deployment/update
                // Pass argument để báo dashboard cần refresh bot list
                Navigator.of(context).pushNamedAndRemoveUntil(
                  AppConstants.dashboardRoute,
                  (route) => false, // Clear all routes in the stack
                  arguments: {'refreshBots': true}, // Báo dashboard cần refresh
                );
              },
              child: Text(
                "OK",
                style: TextStyle(color: Theme.of(context).colorScheme.primary),
              ),
            ),
          ],
        );
      },
    );
  }

  // Show error snackbar with Vietnamese translation
  void _showErrorSnackBar(String message) {
    // Translate common error messages to Vietnamese
    String translatedMessage = _translateErrorMessage(message);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(translatedMessage),
        backgroundColor:
            ThemeReflector.statusColor(context, status: StatusType.error),
      ),
    );
  }

  // Keep error messages in English for debugging
  String _translateErrorMessage(String message) {
    // Return original English message for easier debugging
    return message;
  }

  // Balance checking removed - handled by backend

  // Method to load bot data when in edit mode
  void _loadBotData(int botId) async {
    print("_loadBotData called with botId: $botId");
    final theme = Theme.of(context);

    try {
      setState(() {
        _isDeploying = true;
      });

      // Load bot data from service
      print("Calling _botService.getBotById");
      final bot = await _botService.getBotById(botId);
      print("Bot data loaded: ${bot.name}");

      // Update bot config provider with the bot data
      final botConfigProvider =
          Provider.of<BotConfigProvider>(context, listen: false);
      // Update the provider with the bot data (this would need implementation in your provider)
      // botConfigProvider.loadFromExistingBot(bot);
      print("Provider updated with bot data");

      setState(() {
        _isDeploying = false;
      });
      print("Finished loading bot data, _isEditMode: $_isEditMode");
    } catch (e) {
      print("Error loading bot data: $e");
      setState(() {
        _isDeploying = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load bot data: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Format bot indicators for API
  List<Map<String, dynamic>> _formatBotIndicators(BotConfig config) {
    List<Map<String, dynamic>> indicators = [];

    // Debug entry indicators
    print('=== ENTRY INDICATORS DEBUG ===');
    print('Entry indicators count: ${config.entries.entryIndicators.length}');
    print('Entry indicators: ${config.entries.entryIndicators}');
    print('Min indicators required: ${config.entries.minIndicatorsRequired}');
    print('===============================');

    // Entry indicators - use what user actually selected
    List<String> entryIndicators = config.entries.entryIndicators;

    // For simple bot, if no indicators selected, add default ones
    // Based on Android validation: simple bot needs entry conditions
    final Map<String, dynamic>? routeArgs =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final String botType =
        routeArgs?['botType']?.toString().toLowerCase() ?? 'advanced';

    if (botType == 'simple' && entryIndicators.isEmpty) {
      entryIndicators = ['RSI (14, 30, 70)', 'Bollinger Band (20, 2.0)'];
      print(
          'Simple bot requires entry conditions. Added default indicators: $entryIndicators');
    }

    int entryCounter = 0;
    for (var indicator in entryIndicators) {
      entryCounter++;
      indicators.add({
        'id': null,
        'name': indicator.contains('(')
            ? indicator.split('(')[0].trim()
            : indicator.trim(),
        'description': null,
        'period_num':
            20, // Default values, in a real app these would be from user input
        'value2': 2,
        'value3': 2,
        'value4': null,
        'value5': null,
        'value6': null,
        'updated_at': null,
        'created_at': null,
        'indicator_id': _getIndicatorId(indicator),
        'ind_key':
            DateTime.now().millisecondsSinceEpoch + entryCounter, // Unique key
        'type': 'entry'
      });
    }

    // Exit indicators
    int exitCounter = 0;
    for (var indicator in config.exits.exitIndicators) {
      exitCounter++;
      indicators.add({
        'id': null,
        'name': indicator.contains('(')
            ? indicator.split('(')[0].trim()
            : indicator.trim(),
        'description': null,
        'period_num': 20, // Default values
        'value2': 2,
        'value3': 2,
        'value4': null,
        'value5': null,
        'value6': null,
        'updated_at': null,
        'created_at': null,
        'indicator_id': _getIndicatorId(indicator),
        'ind_key': DateTime.now().millisecondsSinceEpoch +
            1000 +
            exitCounter, // Unique key
        'type': 'exit'
      });
    }

    print('=== FINAL BOT INDICATORS ===');
    print('Total indicators: ${indicators.length}');
    print('Indicators: $indicators');
    print('============================');

    return indicators;
  }

  // Helper to map indicator names to IDs
  int _getIndicatorId(String indicatorName) {
    Map<String, int> indicatorMap = {
      'Bollinger Band': 1,
      'RSI': 2,
      'EMA': 3,
      'MACD': 4,
      'Stochastic': 5
    };

    for (var key in indicatorMap.keys) {
      if (indicatorName.contains(key)) {
        return indicatorMap[key]!;
      }
    }

    return 1; // Default to Bollinger Band
  }

  // Validate percentage values
  double _validatePercentage(double value, double max) {
    // Log for debugging
    print("Validating percentage value: $value against max: $max");

    if (value.isNaN || value.isInfinite) {
      print("Warning: Invalid percentage value ($value). Using default of 0.0");
      return 0.0;
    }

    if (value < 0) {
      print("Warning: Negative percentage value ($value%). Using 0.0 instead.");
      return 0.0;
    }

    if (value > max) {
      print(
          "Warning: Value ($value%) exceeds maximum allowed percentage ($max%). Capping at $max%");
      return max;
    }

    return value;
  }

  // Helper to parse daisy chain value from the displayed text
  int _parseDaisyChain(String daisyChainText) {
    if (daisyChainText.toLowerCase().contains('activated') ||
        daisyChainText.toLowerCase().contains('enabled')) {
      return 1;
    }
    return 0;
  }

  // Helper to parse first in daisy chain value from the displayed text
  int _parseFirstInDaisyChain(String daisyChainText) {
    if (daisyChainText.toLowerCase().contains('first')) {
      return 1;
    }
    return 0;
  }

  // Build API parameters using new factory architecture
  Map<String, dynamic> _buildApiParamsWithFactory(
    BotConfigProvider configFromProvider,
    int exchangePairId,
    int exchangeId,
    String strategy,
  ) {
    final routeArgs =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final String botType =
        routeArgs?['botType']?.toString().toLowerCase() ?? 'advanced';

    // Debug: Check which bot type is being used
    print('=== BOT TYPE DEBUG ===');
    print('Bot type from route: $botType');
    print('Creating payload builder for: $botType');
    print('=====================');

    // Create appropriate payload builder using factory
    final builder = BotPayloadBuilderFactory.createBuilder(
      botType: botType,
      botConfig: configFromProvider.botConfig,
      exchangePairId: exchangePairId,
      exchangeId: exchangeId,
      strategy: strategy,
      isEditMode: _isEditMode,
      botId: _isEditMode ? _getBotIdFromArguments() : null,
    );

    print('=== PAYLOAD BUILDER DEBUG ===');
    print('Builder type: ${builder.runtimeType}');
    print('Builder bot type: ${builder.botType}');
    print('=============================');

    // Build and return payload
    final payload = builder.buildPayload();

    // Debug: Print payload to compare with working payload
    print('=== GENERATED PAYLOAD DEBUG ===');
    print('Bot Type: $botType');
    print('Payload: $payload');
    print('=== CRITICAL FIELDS CHECK ===');
    print('bot_indicators: ${payload['bot_indicators']}');
    print('bot_indicators length: ${payload['bot_indicators']?.length ?? 0}');
    print('indicator_triggers_entry: ${payload['indicator_triggers_entry']}');
    print('type: ${payload['type']}');
    print('initial_fund: ${payload['initial_fund']}');
    print('==============================');
    print('Grid specific fields:');
    if (botType == 'grid') {
      print('  grid_order_vol: ${payload['grid_order_vol']}');
      print('  grid_order_num: ${payload['grid_order_num']}');
      print('  extra_price_gap_min: ${payload['extra_price_gap_min']}');
      print(
          '  indicator_triggers_entry: ${payload['indicator_triggers_entry']}');
    }
    print('===============================');

    return payload;
  }

  // Legacy build API parameters method (kept for reference, will be removed)
  Map<String, dynamic> _buildApiParamsLegacy(
    BotConfigProvider configFromProvider,
    int exchangePairId,
    int exchangeId,
    String strategy,
    double baseOrderPercentage,
    double extraOrderPercentage,
    double takeProfit,
    double stopLoss,
    double minPriceGap,
  ) {
    final routeArgs =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final String botType =
        routeArgs?['botType']?.toString().toLowerCase() ?? 'advanced';

    // Get all user inputs from trade parameters
    final tradeParams = configFromProvider.botConfig.subsequentOrders;

    // Get fund allocation from summary (where it's actually saved)
    final String fundAllocationText =
        configFromProvider.botConfig.summary.fundAllocation;
    final double initialFund = _parseInitialFund(fundAllocationText);

    // Debug fund allocation
    print('=== FUND ALLOCATION DEBUG ===');
    print('fundAllocationText: $fundAllocationText');
    print('parsed initialFund: $initialFund');
    print('Bot type: $botType');

    // Ensure fund is never 0 - use minimum 1 if parsed as 0 (for non-grid bots)
    final double safeFund = initialFund > 0 ? initialFund : 1.0;
    print('safeFund (final): $safeFund');
    print('=============================');

    // Get order types from user inputs (not hardcoded)
    final String orderTypeEntry = tradeParams.orderTypeEntry ?? 'market';
    final String orderTypeExit = tradeParams.orderTypeExit ?? 'market';
    final String baseOrderTypeValue = tradeParams.baseOrderType ?? 'static';
    final int extendedTradingHourValue =
        (tradeParams.extendedTradingHour ?? false) ? 1 : 0;
    final int frequencyValue =
        _parseFrequency(tradeParams.tradingFrequency ?? '15m');

    // Base parameters for all bot types - ALL FROM USER INPUTS
    Map<String, dynamic> params = {
      'apply_to_current_deal': false,
      'avg_entry_price':
          botType == 'grid' ? 0 : safeFund, // Grid: 0, Others: safeFund
      'back_test_time_frame': 365,
      'base_order_percentage': baseOrderPercentage,
      'base_order_type': baseOrderTypeValue, // From user input
      'base_price_gap_min': botType == 'grid'
          ? ""
          : safeFund.toString(), // Grid: empty, Others: safeFund
      'bot_indicators': _formatBotIndicators(configFromProvider.botConfig),
      'daisy_chain': 0,
      'dca_entry_type': "time",
      'dca_value': "daily",
      'dca_value2': "1",
      'dca_value3': 0,
      'disable_after_deal_closed': 0,
      'end_day_auto_close': 0,
      'entry_price': botType == 'grid'
          ? ""
          : safeFund.toString(), // Grid: empty, Others: safeFund
      'exchange_id': exchangeId,
      'exchange_pair_id': exchangePairId,
      'extended_trading_hour': extendedTradingHourValue, // From user input
      'extra_order_percentage': extraOrderPercentage,
      'extra_price_gap_min': minPriceGap,
      'first_in_daisy_chain': 0,
      'fractional_trading_disabled': 0,
      'frequency': frequencyValue, // From user input
      'id': _isEditMode ? _getBotIdFromArguments() : null,
      'indicator_triggers_entry':
          configFromProvider.botConfig.entries.entryIndicators.isNotEmpty
              ? 1
              : 0, // At least 1 if has indicators
      'indicator_triggers_exit': 0,
      'min_tp': botType == 'grid' ? 0 : safeFund, // Grid: 0, Others: safeFund
      'name': configFromProvider.botConfig.summary.botName,
      'off_after_close_deal': 0,
      'order_filled_email_enabled': 1,
      'order_type': orderTypeEntry, // From user input
      'order_type_exit': orderTypeExit, // From user input
      'sell_price':
          botType == 'grid' ? 0 : safeFund, // Grid: 0, Others: safeFund
      'stop_loss': stopLoss,
      'strategy': strategy,
      'tps': [],
      'trading_hour_247': 0,
      'tsls': [],
      'type': botType,
    };

    // Add initial_fund for non-grid bots only
    if (botType != 'grid') {
      params['initial_fund'] = safeFund; // Non-grid bots need initial_fund
    }

    // Grid-specific parameters
    if (botType == 'grid') {
      // Get Grid-specific values from user inputs in trade_parameter_screen
      final subsequentOrders = configFromProvider.botConfig.subsequentOrders;
      final upperRange = subsequentOrders.gridUpperRange ?? "0"; // User input
      final lowerRange = subsequentOrders.gridLowerRange ?? "0"; // User input
      final orderNum = subsequentOrders.gridOrderNum ?? "0"; // User input
      final orderVol =
          subsequentOrders.gridOrderVol ?? "0"; // Get from user input

      // Validate grid_order_vol - only use default if user input is 0 or empty
      final double orderVolValue = double.tryParse(orderVol) ?? 0.0;
      final String validOrderVol = orderVolValue > 0
          ? orderVol
          : "4"; // Fallback to 4 to match working payload (4*3=12 total)

      // Validate grid_order_num - ensure it's not 0
      final int orderNumValue = int.tryParse(orderNum) ?? 0;
      final String validOrderNum = orderNumValue > 0
          ? orderNum
          : "3"; // Default 3 orders to match working payload

      print('=== GRID USER INPUT ANALYSIS ===');
      print(
          'upperRange: "$upperRange" (${upperRange.isEmpty ? "EMPTY" : "HAS VALUE"})');
      print(
          'lowerRange: "$lowerRange" (${lowerRange.isEmpty ? "EMPTY" : "HAS VALUE"})');
      print(
          'orderNum: "$orderNum" (${orderNum.isEmpty ? "EMPTY" : "HAS VALUE"})');
      print(
          'orderVol: "$orderVol" (${orderVol.isEmpty ? "EMPTY" : "HAS VALUE"})');
      print(
          'orderVolValue: $orderVolValue (${orderVolValue == 0 ? "ZERO" : "NON-ZERO"})');
      print('validOrderVol: "$validOrderVol"');
      print('=== POTENTIAL FUND ISSUES ===');
      print(
          'Is orderVol zero or empty? ${orderVol == "0" || orderVol.isEmpty}');
      print(
          'Is orderNum zero or empty? ${orderNum == "0" || orderNum.isEmpty}');

      // Calculate total fund requirement
      final double totalOrderVol = double.tryParse(validOrderVol) ?? 0.0;
      final int totalOrderNum = int.tryParse(validOrderNum) ?? 0;
      final double totalFundRequired = totalOrderVol * totalOrderNum;

      print('Total fund calculation:');
      print('  validOrderVol: $validOrderVol');
      print('  validOrderNum: $validOrderNum');
      print('  totalFundRequired: $totalFundRequired');
      print('Backend might be checking this total against available balance');
      print('================================');
      final outerRange =
          _parsePercentageValue(configFromProvider.botConfig.exits.takeProfit);

      // Map entry type to grid_entry_type
      String gridEntryType = "imd"; // Default to immediate

      // Price bot always uses "imd" regardless of entry type selection
      if (botType == 'price') {
        gridEntryType = "imd";
      } else {
        // For other bot types (grid), map based on entry type selection
        switch (configFromProvider.botConfig.entries.entryType) {
          case 'Range':
            gridEntryType = "range";
            break;
          case 'Indicator':
            gridEntryType = "osc";
            break;
          case 'Immediate':
          default:
            gridEntryType = "imd";
            break;
        }
      }

      params.addAll({
        'grid_entry_type': gridEntryType, // Map from user selection
        'grid_exit_outer_range': outerRange.toString(), // Từ user input
        'grid_exit_strategy_has_trade': "stop_bot", // Match working payload
        'grid_exit_strategy_no_trade': "restart_bot", // Match working payload
        'grid_exit_wait_time': 1, // Match working payload
        'grid_lower_range': lowerRange, // Từ user input
        'grid_order_num': validOrderNum, // Validated user input (never 0)
        'grid_order_vol': validOrderVol, // Validated user input (never 0)
        'grid_range_kline_direction': 0,
        'grid_upper_range': upperRange, // Từ user input
        // Grid bot doesn't use grid_buy_side_fund/grid_sell_side_fund in this format
      });

      // Add range-specific parameters if entry type is Range
      if (gridEntryType == "range") {
        final entries = configFromProvider.botConfig.entries;
        params.addAll({
          'grid_range_kline_value': entries.rangePercentage ?? '5', // % change
          'grid_range_kline_num': entries.rangePeriods ?? '3', // Periods
          'grid_range_kline_direction':
              (entries.isRangeLesser ?? true) ? 0 : 1, // 0=lesser, 1=greater
        });
      }
    }

    // Momentum-specific parameters
    if (botType == 'momentum') {
      // Parse actual values từ user inputs
      final baseTrigger = _parsePercentageValue(
          configFromProvider.botConfig.entries.baseOrderLimit);
      final extraTrigger = _parsePercentageValue(
          configFromProvider.botConfig.subsequentOrders.extraOrders);

      // Momentum bot chỉ có 1 extra order (base + extra = 2 trades total)
      params.addAll({
        'base_price_gap_min':
            baseTrigger.toString(), // Base Order trigger % từ user input
        'extra_price_gap_min':
            extraTrigger, // Extra Order trigger % từ user input
        'extra_order_percentage': 100.0, // Chỉ 1 extra order = 100%
        'indicator_triggers_exit': 0,
        // Note: initial_fund already added in base params for non-grid bots
      });

      // Momentum không cần grid parameters
      params.remove('grid_entry_type');
      params.remove('grid_exit_outer_range');
      params.remove('grid_exit_strategy_has_trade');
      params.remove('grid_exit_strategy_no_trade');
      params.remove('grid_exit_wait_time');
      params.remove('grid_lower_range');
      params.remove('grid_order_num');
      params.remove('grid_order_vol');
      params.remove('grid_range_kline_direction');
      params.remove('grid_upper_range');

      // Remove some base params that are not needed for momentum
      params.remove('tps');
      params.remove('tsls');
    }

    return params;
  }
}
