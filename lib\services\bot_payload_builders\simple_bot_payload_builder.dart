import 'dart:convert';

import 'bot_payload_builder.dart';

/// Simple bot specific payload builder
class SimpleBotPayloadBuilder extends BotPayloadBuilder {
  SimpleBotPayloadBuilder({
    required super.botConfig,
    required super.exchangePairId,
    required super.exchangeId,
    required super.strategy,
    required super.isEditMode,
    super.botId,
  });

  @override
  String get botType => 'simple';

  @override
  Map<String, dynamic> buildPayload() {
    // Parse user inputs
    final initialFund = _getInitialFund();
    final baseOrderPercentage = _getBaseOrderPercentage();
    final extraOrderPercentage = _getExtraOrderPercentage();
    final takeProfit = parsePercentageValue(botConfig.exits.takeProfit);
    final stopLoss = parsePercentageValue(botConfig.exits.stopLoss ?? '0');
    final minPriceGap =
        parsePercentageValue(botConfig.subsequentOrders.minPriceGap);

    // Base payload structure for simple bot
    Map<String, dynamic> payload = {
      'apply_to_current_deal': false,
      'avg_entry_price': initialFund,
      'back_test_time_frame': 365,
      'base_order_percentage': baseOrderPercentage,
      'base_order_type': _getSimpleBotBaseOrderType(),
      'base_price_gap_min': _getSimpleBotMinPriceGap(),
      'bot_indicators': _getSimpleBotIndicators(),
      'daisy_chain': 0,
      'dca_entry_type': "time",
      'dca_value': "daily",
      'dca_value2': "1",
      'dca_value3': 0,
      'disable_after_deal_closed': 0,
      'end_day_auto_close': 0,
      'entry_price': initialFund.toString(),
      'exchange_id': exchangeId,
      'exchange_pair_id': exchangePairId,
      'extended_trading_hour': botConfig.summary.tradeExtendedHour ? 1 : 0,
      'extra_order_percentage': extraOrderPercentage,
      'extra_price_gap_min': minPriceGap,
      'first_in_daisy_chain': 0,
      'fractional_trading_disabled': 0,
      'frequency': getFrequencyValue(),
      'id': isEditMode ? botId : null,
      'indicator_triggers_entry': _getIndicatorTriggersEntry(),
      'indicator_triggers_exit': _getIndicatorTriggersExit(),
      'initial_fund': initialFund, // Simple bot needs initial_fund
      'min_tp': initialFund,
      'name': botConfig.summary.botName,
      'off_after_close_deal': 0,
      'order_filled_email_enabled': 1,
      'order_type': getOrderTypeValue(botConfig.entries.orderType),
      'order_type_exit': _getSimpleBotExitOrderType(),
      'profit': takeProfit,
      'sell_price': initialFund,
      'stop_loss': stopLoss,
      'strategy': strategy, // Từ user selection, không hardcode
      'tps': [],
      'trading_hour_247': 0,
      'tsls': [],
      'type': botType,
    };

    // Debug: Log complete simple bot payload for comparison
    print('=== SIMPLE BOT COMPLETE PAYLOAD DEBUG ===');
    print('Bot type: $botType (SIMPLE BOT ONLY)');
    print('FULL PAYLOAD JSON:');
    print(jsonEncode(payload));
    print('');
    print('PAYLOAD BREAKDOWN:');
    print('strategy: ${payload['strategy']}');
    print('base_price_gap_min: ${payload['base_price_gap_min']}');
    print('indicator_triggers_entry: ${payload['indicator_triggers_entry']}');
    print('indicator_triggers_exit: ${payload['indicator_triggers_exit']}');
    print('profit: ${payload['profit']}');
    print('stop_loss: ${payload['stop_loss']}');
    print('order_type_exit: ${payload['order_type_exit']}');
    print('bot_indicators count: ${payload['bot_indicators']?.length ?? 0}');
    print('bot_indicators structure:');
    for (int i = 0; i < (payload['bot_indicators']?.length ?? 0); i++) {
      print('  Indicator $i: ${payload['bot_indicators'][i]}');
    }
    print('');
    print('CONFIG DATA:');
    print('Entry indicators from config: ${botConfig.entries.entryIndicators}');
    print('Exit indicators from config: ${botConfig.exits.exitIndicators}');
    print(
        'Min entry indicators required: ${botConfig.entries.minIndicatorsRequired}');
    print(
        'Min exit indicators required: ${botConfig.exits.minIndicatorsRequired}');
    print('==========================================');

    return payload;
  }

  /// Get initial fund with validation
  double _getInitialFund() {
    final initialFund = parseInitialFund(botConfig.summary.fundAllocation);
    return initialFund > 0 ? initialFund : 1000.0; // Default 1000
  }

  /// Get base order percentage with validation
  double _getBaseOrderPercentage() {
    double percentage = parsePercentageValue(botConfig.entries.baseOrderLimit);
    if (percentage > 100) percentage = 100;
    if (percentage <= 0) percentage = 50; // Default 50%
    return percentage;
  }

  /// Get extra order percentage with validation
  double _getExtraOrderPercentage() {
    double percentage =
        parsePercentageValue(botConfig.subsequentOrders.extraOrders);
    if (percentage > 100) percentage = 100;
    if (percentage <= 0) percentage = 25; // Default 25%
    return percentage;
  }

  /// Get base order type for Simple bot (different from other bots)
  String _getSimpleBotBaseOrderType() {
    final originalType = botConfig.entries.baseOrderType;

    print('=== SIMPLE BOT BASE ORDER TYPE DEBUG ===');
    print('Original base order type: $originalType');

    // Simple bot might need different mapping
    // Try using the original value directly first
    final convertedType = originalType.toLowerCase();

    print('Simple bot base order type: $convertedType');
    print('========================================');

    return convertedType;
  }

  /// Get exit order type for Simple bot (like Quickstart)
  String _getSimpleBotExitOrderType() {
    // Simple bot: always use entry order type (like Quickstart)
    final exitOrderType = getOrderTypeValue(botConfig.entries.orderType);

    print('=== EXIT ORDER TYPE DEBUG ===');
    print('Entry order type from config: ${botConfig.entries.orderType}');
    print('Converted exit order type: $exitOrderType');
    print('=============================');

    return exitOrderType;
  }

  /// Get minimum price gap for Simple bot (like Quickstart)
  String _getSimpleBotMinPriceGap() {
    // Use same logic as Quickstart: parse and return as string if > 0
    final minPriceGap =
        parsePercentageValue(botConfig.subsequentOrders.minPriceGap);

    print('=== MIN PRICE GAP DEBUG ===');
    print(
        'Raw minPriceGap from config: "${botConfig.subsequentOrders.minPriceGap}"');
    print('Parsed minPriceGap value: $minPriceGap');

    // Simple bot: return actual value as string (like Quickstart extra_price_gap_min)
    if (minPriceGap > 0) {
      print('Returning minPriceGap: ${minPriceGap.toString()}');
      print('===========================');
      return minPriceGap.toString();
    } else {
      print('MinPriceGap <= 0, returning empty string');
      print('===========================');
      return '';
    }
  }

  /// Get indicators for simple bot (with proper type assignment)
  List<Map<String, dynamic>> _getSimpleBotIndicators() {
    List<Map<String, dynamic>> indicators = [];

    print('=== SIMPLE BOT INDICATORS DEBUG ===');
    print('Entry indicators from config: ${botConfig.entries.entryIndicators}');
    print('Exit indicators from config: ${botConfig.exits.exitIndicators}');

    // Add entry indicators with type = "entry"
    for (String indicator in botConfig.entries.entryIndicators) {
      Map<String, dynamic> parsedIndicator = _parseIndicator(indicator);
      parsedIndicator['type'] = 'entry'; // ✅ Set type correctly
      indicators.add(parsedIndicator);
    }

    // Add exit indicators with type = "exit"
    for (String indicator in botConfig.exits.exitIndicators) {
      Map<String, dynamic> parsedIndicator = _parseIndicator(indicator);
      parsedIndicator['type'] = 'exit'; // ✅ Set type correctly
      indicators.add(parsedIndicator);
    }

    // Simple bot requires entry conditions - add defaults if none (like Quickstart)
    if (botConfig.entries.entryIndicators.isEmpty &&
        botConfig.exits.exitIndicators.isEmpty) {
      print('No indicators found, adding simple defaults...');
      indicators = [
        _parseIndicator('RSI (14, 30, 70)'), // Will have type = "entry"
        _parseIndicator('MACD (12, 26, 9)'), // Will have type = "entry"
      ];
      print('Simple default indicators added: $indicators');
    } else {
      print('Using user-selected indicators with proper types: $indicators');
    }

    print('Final indicators for simple bot: $indicators');
    print('Final indicators count: ${indicators.length}');
    print('====================================');

    return indicators;
  }

  /// Get indicator triggers entry count (simplified like quickstart)
  int _getIndicatorTriggersEntry() {
    final entryIndicators = botConfig.entries.entryIndicators;

    print('=== ENTRY INDICATOR TRIGGERS DEBUG ===');
    print('Entry indicators: $entryIndicators');
    print('Entry indicators count: ${entryIndicators.length}');
    print(
        'Min indicators required: ${botConfig.entries.minIndicatorsRequired}');

    // Simple bot logic (like quickstart):
    if (entryIndicators.isEmpty) {
      print('No entry indicators selected - using 1 default trigger');
      return 1; // Default for simple bot (like quickstart)
    }

    final triggers =
        parseMinIndicators(botConfig.entries.minIndicatorsRequired);
    print('Parsed entry triggers: $triggers');
    print('======================================');

    return triggers;
  }

  /// Get indicator triggers exit count (simplified like quickstart)
  int _getIndicatorTriggersExit() {
    final exitIndicators = botConfig.exits.exitIndicators;

    print('=== EXIT INDICATOR TRIGGERS DEBUG ===');
    print('Exit indicators: $exitIndicators');
    print('Exit indicators count: ${exitIndicators.length}');
    print(
        'Min exit indicators required: ${botConfig.exits.minIndicatorsRequired}');

    // Simple bot logic (like quickstart):
    if (exitIndicators.isEmpty) {
      print('No exit indicators selected - using 0 exit triggers');
      return 0; // No exit indicators required for simple bot
    }

    final triggers = parseMinIndicators(botConfig.exits.minIndicatorsRequired);
    print('Parsed exit triggers: $triggers');
    print('=====================================');

    return triggers;
  }

  /// Format indicators using API data instead of parsing strings
  List<Map<String, dynamic>> _formatIndicatorsWithApiData(config) {
    List<Map<String, dynamic>> indicators = [];

    // Add entry indicators with API data
    for (String indicatorName in config.entries.entryIndicators) {
      final apiData = _getApiIndicatorData(indicatorName);
      if (apiData.isNotEmpty) {
        indicators.add(apiData);
      }
    }

    // Add exit indicators with API data
    for (String indicatorName in config.exits.exitIndicators) {
      final apiData = _getApiIndicatorData(indicatorName);
      if (apiData.isNotEmpty) {
        indicators.add(apiData);
      }
    }

    return indicators;
  }

  /// Get indicator data from API response based on name
  Map<String, dynamic> _getApiIndicatorData(String indicatorName) {
    // API response data from your example
    final apiIndicators = {
      'Bollinger Band': {
        'id': 1,
        'name': 'Bollinger Band',
        'description': null,
        'period_num': 20,
        'value2': 2,
        'value3': 2,
        'value4': null,
        'value5': null,
        'value6': null,
      },
      'EMA': {
        'id': 2,
        'name': 'EMA',
        'description': null,
        'period_num': 50,
        'value2': 9,
        'value3': null,
        'value4': null,
        'value5': null,
        'value6': null,
      },
      'Stochastic RSI': {
        'id': 3,
        'name': 'Stochastic RSI',
        'description': null,
        'period_num': 14,
        'value2': 18.5,
        'value3': 81.5,
        'value4': 1,
        'value5': 18.5,
        'value6': 81.5,
      },
      'RSI': {
        'id': 4,
        'name': 'RSI',
        'description': null,
        'period_num': 14,
        'value2': 30,
        'value3': 70,
        'value4': 1,
        'value5': 25,
        'value6': 75,
      },
      'MACD': {
        'id': 5,
        'name': 'MACD',
        'description': null,
        'period_num': 12,
        'value2': 26,
        'value3': 9,
        'value4': null,
        'value5': null,
        'value6': null,
      },
      'Volume': {
        'id': 6,
        'name': 'Volume',
        'description': null,
        'period_num': 2,
        'value2': 0,
        'value3': 0,
        'value4': null,
        'value5': null,
        'value6': null,
      },
    };

    // Find exact match first
    if (apiIndicators.containsKey(indicatorName)) {
      print('Found exact API data for: $indicatorName');
      return Map<String, dynamic>.from(apiIndicators[indicatorName]!);
    }

    // Try partial match for indicators with parameters like "RSI (14, 30, 70)"
    for (String apiName in apiIndicators.keys) {
      if (indicatorName.toLowerCase().contains(apiName.toLowerCase())) {
        print('Found partial API data for: $indicatorName -> $apiName');
        return Map<String, dynamic>.from(apiIndicators[apiName]!);
      }
    }

    print('WARNING: No API data found for indicator: $indicatorName');
    return {};
  }

  /// Parse individual indicator string (with complete structure like Quickstart)
  Map<String, dynamic> _parseIndicator(String indicatorStr) {
    // Parse the indicator string with COMPLETE structure like Quickstart
    if (indicatorStr.contains('RSI')) {
      return {
        'id': null,
        'name': 'RSI',
        'description': null,
        'period_num': 14,
        'value2': 30,
        'value3': 70,
        'value4': null,
        'value5': null,
        'value6': null,
        'value7': null,
        'value8': null,
        'value9': null,
        'value10': null,
        'value11': null,
        'value12': null,
        'value13': null,
        'value14': null,
        'value15': null,
        'value16': null,
        'value17': null,
        'value18': null,
        'value19': null,
        'value20': null,
        // ✅ ADD MISSING FIELDS FROM QUICKSTART
        'ind_key': 4942, // Random key like Quickstart
        'indicator_id': 4, // RSI indicator ID
        'type': 'entry', // Default to entry
        'created_at': null,
        'updated_at': null,
      };
    } else if (indicatorStr.contains('MACD')) {
      return {
        'id': null,
        'name': 'MACD',
        'description': null,
        'period_num': 12,
        'value2': 26,
        'value3': 9,
        'value4': null,
        'value5': null,
        'value6': null,
        'value7': null,
        'value8': null,
        'value9': null,
        'value10': null,
        'value11': null,
        'value12': null,
        'value13': null,
        'value14': null,
        'value15': null,
        'value16': null,
        'value17': null,
        'value18': null,
        'value19': null,
        'value20': null,
        // ✅ ADD MISSING FIELDS FROM QUICKSTART
        'ind_key': 7704, // Random key like Quickstart
        'indicator_id': 5, // MACD indicator ID
        'type': 'entry', // Default to entry
        'created_at': null,
        'updated_at': null,
      };
    } else if (indicatorStr.contains('Bollinger Band')) {
      return {
        'id': null,
        'name': 'Bollinger Band',
        'description': null,
        'period_num': 20,
        'value2': 2,
        'value3': 2,
        'value4': null,
        'value5': null,
        'value6': null,
        'value7': null,
        'value8': null,
        'value9': null,
        'value10': null,
        'value11': null,
        'value12': null,
        'value13': null,
        'value14': null,
        'value15': null,
        'value16': null,
        'value17': null,
        'value18': null,
        'value19': null,
        'value20': null,
        // ✅ ADD MISSING FIELDS FROM QUICKSTART
        'ind_key': 7704, // Random key like Quickstart
        'indicator_id': 1, // Bollinger Band indicator ID
        'type': 'entry', // Default to entry
        'created_at': null,
        'updated_at': null,
      };
    }

    // Default fallback with complete structure
    return {
      'id': null,
      'name': indicatorStr,
      'description': null,
      'period_num': 14,
      'value2': null,
      'value3': null,
      'value4': null,
      'value5': null,
      'value6': null,
      'value7': null,
      'value8': null,
      'value9': null,
      'value10': null,
      'value11': null,
      'value12': null,
      'value13': null,
      'value14': null,
      'value15': null,
      'value16': null,
      'value17': null,
      'value18': null,
      'value19': null,
      'value20': null,
      // ✅ ADD MISSING FIELDS FROM QUICKSTART
      'ind_key': 4942, // Random key
      'indicator_id': 1, // Default indicator ID
      'type': 'entry', // Default to entry
      'created_at': null,
      'updated_at': null,
    };
  }
}
