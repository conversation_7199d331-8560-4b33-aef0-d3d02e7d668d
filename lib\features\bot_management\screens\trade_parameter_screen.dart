import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:stockhero/models/bot_config.dart';
import 'package:stockhero/services/subscription_validation_service.dart';
import 'package:stockhero/shared/controllers/auth_controller.dart';
import 'package:stockhero/shared/providers/bot_config_provider.dart';
import 'package:stockhero/shared/utils/theme_reflector.dart';
import 'package:stockhero/shared/utils/theme_utils.dart';
import "package:stockhero/utils/constants.dart";

class TradeParameterScreen extends StatefulWidget {
  const TradeParameterScreen({Key? key}) : super(key: key);

  @override
  State<TradeParameterScreen> createState() => _TradeParameterScreenState();
}

class _TradeParameterScreenState extends State<TradeParameterScreen> {
  // Fund allocation
  final TextEditingController _fundAllocationController =
      TextEditingController(text: '\$1,000');

  // Radio button states
  String entryOrderType = 'Limit';
  String exitOrderType = 'Limit';
  String baseOrderType = 'Static';

  // Slider values
  double baseOrderLimit = 50;
  double extraOrders = 25;
  double minimumPriceGap = 2.5;

  // Route arguments to pass through
  Map<String, dynamic>? routeArgs;

  // Trading frequency
  String tradingFrequency = '15m';

  // Extended trading hours
  bool tradeExtendedHour = true;

  // Bot details
  String botName = '';
  String brokerage = '';
  String symbol = '';
  String strategy = '';
  int? botId;
  bool isEditing = false;
  bool isCopying = false;
  String baseAsset = '';
  String quoteAsset = '';
  String botType = 'advanced'; // Default to advanced

  // Grid bot specific parameters
  double upperRangeSellPriceLimit = 0.0;
  double lowerRangeBuyPriceLimit = 0.0;
  int numberOfOrdersBetweenMidPriceAndRangeLimit = 0;
  double perOrderAmount = 0.0;
  double estimatedTotalFund = 0.0;
  double totalFundRequiredBuySide = 0.0;
  double totalFundRequiredSellSide = 0.0;
  double fundAvailableBuySide = 0.0; // Will be fetched from API
  double fundAvailableSellSide = 0.0; // Will be fetched from API

  // Fund balances from API
  List<Map<String, dynamic>> userBalances = [];
  bool isLoadingBalances = false;

  @override
  void initState() {
    super.initState();
    print(
        "TradeParameterScreen initState: minimumPriceGap=$minimumPriceGap, baseOrderLimit=$baseOrderLimit, extraOrders=$extraOrders");

    // Đảm bảo không có giá trị nào = 0
    if (baseOrderLimit == 0) baseOrderLimit = 0.1;
    if (extraOrders == 0) extraOrders = 0.1;
    if (minimumPriceGap == 0) minimumPriceGap = 0.5;

    // Ensure these values are capped at 100% total on initialization
    if (baseOrderLimit > 100) baseOrderLimit = 100;
    if (extraOrders > 100) extraOrders = 100;
    if (baseOrderLimit + extraOrders > 100) {
      extraOrders = 100 - baseOrderLimit;
      if (extraOrders < 0.1) extraOrders = 0.1;
    }

    // Đảm bảo minimumPriceGap không nhỏ hơn 0.5
    if (minimumPriceGap < 0.5) minimumPriceGap = 0.5;

    print(
        "TradeParameterScreen after init validation: minimumPriceGap=$minimumPriceGap, baseOrderLimit=$baseOrderLimit, extraOrders=$extraOrders");

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _processRouteArguments();

      // Kiểm tra sau khi xử lý tham số
      Future.delayed(const Duration(milliseconds: 500), () {
        _verifyAllSliderValues();
      });
    });
  }

  // Balance fetching removed - handled by backend

  void _processRouteArguments() {
    final Map<String, dynamic>? args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      print("TradeParameterScreen received route arguments: $args");

      // Store all route args to pass through to next screen
      routeArgs = args;

      safeSetState(() {
        // Thông tin cơ bản
        botName = args['botName'] ?? '';
        brokerage = args['brokerage'] ?? '';
        symbol = args['symbol'] ?? '';
        strategy = args['strategy'] ?? 'Long';
        botId = args['botId'];
        isEditing = args['isEditing'] ?? false;
        isCopying = args['isCopying'] ?? false;
        baseAsset = args['baseAsset'] ?? '';
        quoteAsset = args['quoteAsset'] ?? '';
        botType = args['botType'] ?? 'advanced'; // Get botType from arguments

        // Log specific values for debugging
        print("Bot Name: $botName");
        print("Brokerage: $brokerage");
        print("Symbol: $symbol");
        print("Base Asset: $baseAsset");
        print("Quote Asset: $quoteAsset");
        print("Strategy: $strategy");
        print("Bot Type: $botType");
        print("Editing: $isEditing, Copying: $isCopying");

        // Apply default parameters based on bot type if not editing
        Map<String, dynamic> defaultParams =
            _getDefaultParametersForBotType(botType);

        // Thông tin tham số giao dịch - không tự động điền
        String initialFundText = args['initialFund']?.toString() ?? '';
        _fundAllocationController.text = initialFundText;

        // Cập nhật trạng thái radio button
        entryOrderType = _capitalizeFirstLetter(
            args['orderTypeEntry'] ?? defaultParams['orderTypeEntry']);
        exitOrderType = _capitalizeFirstLetter(
            args['orderTypeExit'] ?? defaultParams['orderTypeExit']);
        baseOrderType = _capitalizeFirstLetter(
            args['baseOrderType'] ?? defaultParams['baseOrderType']);

        // Cập nhật giá trị slider
        double tempBaseOrderLimit = args['baseOrderPercentage'] != null
            ? (args['baseOrderPercentage'] is num
                ? (args['baseOrderPercentage'] as num).toDouble()
                : double.tryParse(args['baseOrderPercentage'].toString()) ??
                    defaultParams['baseOrderPercentage'])
            : defaultParams['baseOrderPercentage'];
        double tempExtraOrders = args['extraOrderPercentage'] != null
            ? (args['extraOrderPercentage'] is num
                ? (args['extraOrderPercentage'] as num).toDouble()
                : double.tryParse(args['extraOrderPercentage'].toString()) ??
                    defaultParams['extraOrderPercentage'])
            : defaultParams['extraOrderPercentage'];
        double tempMinimumPriceGap = args['minPriceGap'] != null
            ? (args['minPriceGap'] is num
                ? (args['minPriceGap'] as num).toDouble()
                : double.tryParse(args['minPriceGap'].toString()) ??
                    defaultParams['minPriceGap'])
            : defaultParams['minPriceGap'];

        // Đảm bảo các giá trị đều hợp lệ
        if (tempBaseOrderLimit < 0.1) tempBaseOrderLimit = 0.1;
        if (tempExtraOrders < 0.1) tempExtraOrders = 0.1;
        if (tempMinimumPriceGap < 0.5) tempMinimumPriceGap = 0.5;

        // Cap these values
        if (tempBaseOrderLimit > 100) tempBaseOrderLimit = 100;
        if (tempExtraOrders > 100) tempExtraOrders = 100;
        if (tempBaseOrderLimit + tempExtraOrders > 100) {
          tempExtraOrders = 100 - tempBaseOrderLimit;
          if (tempExtraOrders < 0) tempExtraOrders = 0;
        }
        if (tempMinimumPriceGap > 10) tempMinimumPriceGap = 10;

        baseOrderLimit = tempBaseOrderLimit;
        extraOrders = tempExtraOrders;
        minimumPriceGap = tempMinimumPriceGap;

        // Cập nhật tần suất giao dịch
        String frequencyFromArgs =
            args['frequency']?.toString() ?? defaultParams['frequency'];
        tradingFrequency = frequencyFromArgs;
        print(
            "Trading frequency set to: $tradingFrequency (from args: ${args['frequency']}, default: ${defaultParams['frequency']})");

        // Cập nhật extended trading hour
        tradeExtendedHour =
            args['extendedTradingHour'] ?? defaultParams['extendedTradingHour'];

        // Log all parameters after processing
        print("Processed Arguments:");
        print("Initial Fund: ${_fundAllocationController.text}");
        print("Entry Order Type: $entryOrderType");
        print("Exit Order Type: $exitOrderType");
        print("Base Order Type: $baseOrderType");
        print("Base Order Limit: $baseOrderLimit%");
        print("Extra Orders: $extraOrders%");
        print("Minimum Price Gap: $minimumPriceGap%");
        print("Trading Frequency: $tradingFrequency");
        print("Trade Extended Hour: $tradeExtendedHour");
      });
    } else {
      print("WARNING: No route arguments received in TradeParameterScreen!");
      // Đảm bảo frequency mặc định là 15m khi không có arguments
      safeSetState(() {
        tradingFrequency = '15m';
      });
      print("Set default trading frequency to: $tradingFrequency");
    }
  }

  // Helper method to convert frequency int to string format
  String _frequencyToString(int minutes) {
    if (minutes < 5) return '1m';
    if (minutes < 15) return '5m';
    if (minutes < 60) return '15m';
    if (minutes < 120) return '1H';
    if (minutes < 240) return '2H';
    if (minutes < 1440) return '4H';
    return '1D';
  }

  // Helper method to capitalize first letter
  String _capitalizeFirstLetter(String text) {
    if (text.isEmpty) return '';
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  @override
  void dispose() {
    _fundAllocationController.dispose();
    super.dispose();
  }

  // Helper method để safely call setState với mounted check
  void safeSetState(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Ensure values are within valid ranges
    _validateAndCapValues();

    // Get theme colors
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Scaffold(
      backgroundColor: ThemeReflector.screenBackground(context),
      appBar: AppBar(
        backgroundColor: ThemeReflector.surfaceColor(context),
        elevation: 0,
        title: Text(
          isEditing ? 'Edit Bot' : (isCopying ? 'Copy Bot' : 'Create Bot'),
          style: TextStyle(
            color: ThemeReflector.textColor(
              context,
              importance: TextImportance.primary,
            ),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: ThemeReflector.iconColor(context),
            size: 24,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.close,
              color: ThemeReflector.iconColor(context),
              size: 24,
            ),
            onPressed: () => Navigator.pushNamedAndRemoveUntil(
              context,
              AppConstants.dashboardRoute,
              (route) => false,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with image and title
                  _buildHeader(),

                  // Conditional UI based on bot type
                  if (botType.toLowerCase() == 'grid')
                    ..._buildGridTradeParameterUI()
                  else if (botType.toLowerCase() == 'quickstart')
                    ..._buildQuickstartTradeParameterUI()
                  else ...[
                    // Fund Allocation
                    _buildSection(
                      title: 'Fund Allocation',
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextField(
                            controller: _fundAllocationController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 14),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: ThemeReflector.borderColor(context),
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: ThemeReflector.borderColor(context),
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                              hintText: 'Enter amount',
                              helperText: 'Enter fund allocation amount',
                            ),
                            onChanged: (_) => safeSetState(() {}),
                          ),
                          const SizedBox(height: 10),
                          Row(
                            children: [
                              Icon(
                                Icons.account_balance_wallet,
                                size: 16,
                                color: theme.colorScheme.primary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                symbol.contains('USD')
                                    ? 'Available: USD 9,311.07'
                                    : 'Available on Paper AAPL: 12,059',
                                style: textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          if (baseAsset.isNotEmpty && quoteAsset.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                'Trading $baseAsset/$quoteAsset',
                                style: textTheme.bodySmall,
                              ),
                            ),
                        ],
                      ),
                    ),

                    // Entry Order Type
                    _buildSection(
                      title: 'Entry Order Type',
                      child: _buildRadioGroup(
                        value: entryOrderType,
                        options: const ['Limit', 'Market'],
                        onChanged: (value) {
                          safeSetState(() {
                            entryOrderType = value!;
                          });
                        },
                      ),
                    ),

                    // Exit Order Type
                    _buildSection(
                      title: 'Exit Order Type',
                      child: _buildRadioGroup(
                        value: exitOrderType,
                        options: const ['Limit', 'Market'],
                        onChanged: (value) {
                          safeSetState(() {
                            exitOrderType = value!;
                          });
                        },
                      ),
                    ),

                    // Base Order Type
                    _buildSection(
                      title: 'Base Order Type',
                      child: _buildRadioGroup(
                        value: baseOrderType,
                        options: const ['Static', 'Dynamic'],
                        onChanged: (value) {
                          safeSetState(() {
                            baseOrderType = value!;
                          });
                        },
                      ),
                    ),

                    // Base Order Limit
                    _buildSection(
                      title: 'Base Order Limit',
                      child: _buildSliderWithValue(
                        value: baseOrderLimit,
                        onChanged: (value) {
                          safeSetState(() {
                            // Cap the value at 100
                            if (value > 100) value = 100;
                            if (value < 0.1) value = 0.1;

                            baseOrderLimit = value;

                            // If the total exceeds 100%, adjust extraOrders
                            if (baseOrderLimit + extraOrders > 100) {
                              double newExtraOrders = 100 - baseOrderLimit;
                              // Make sure extraOrders doesn't go below 0
                              if (newExtraOrders < 0) newExtraOrders = 0;
                              extraOrders = newExtraOrders;
                            }
                          });
                        },
                        displayValue: '${baseOrderLimit.toInt()}%',
                        max: 100, // Ensure max is 100
                      ),
                    ),

                    // Extra Orders - Hide for Price bot
                    if (botType != 'price') ...[
                      _buildSection(
                        title: 'Extra Orders',
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSliderWithValue(
                              value: extraOrders,
                              onChanged: (value) {
                                // Validate against user's subscription plan
                                final authController =
                                    Provider.of<AuthController>(context,
                                        listen: false);

                                // Debug log
                                print(
                                    'Extra Orders Validation: User trying to set ${value.toInt()}%');
                                final userTier =
                                    SubscriptionValidationService.getUserTier(
                                        authController);
                                print('User tier: $userTier');

                                final validation = SubscriptionValidationService
                                    .validateExtraOrders(authController, value);

                                if (!validation.isValid) {
                                  print(
                                      'Validation failed: ${validation.message}');
                                  // Show upgrade dialog
                                  SubscriptionValidationService
                                      .showUpgradeDialog(
                                    context,
                                    validation.message!,
                                    validation.requiredTier!,
                                  );
                                  return;
                                } else {
                                  print(
                                      'Validation passed: User can use ${value.toInt()}% extra orders');
                                }

                                safeSetState(() {
                                  // Cap the value at 100
                                  if (value > 100) value = 100;
                                  if (value < 0.1) value = 0.1;

                                  extraOrders = value;

                                  // If the total exceeds 100%, adjust baseOrderLimit
                                  if (baseOrderLimit + extraOrders > 100) {
                                    double newBaseOrderLimit =
                                        100 - extraOrders;
                                    // Make sure baseOrderLimit doesn't go below 0
                                    if (newBaseOrderLimit < 0)
                                      newBaseOrderLimit = 0;
                                    baseOrderLimit = newBaseOrderLimit;
                                  }
                                });
                              },
                              displayValue: '${extraOrders.toInt()}%',
                              max: 100, // Ensure max is 100
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Note: Base Order + Extra Orders cannot exceed 100%. Current total: ${(baseOrderLimit + extraOrders).toInt()}%',
                              style: TextStyle(
                                fontSize: 12,
                                color: (baseOrderLimit + extraOrders > 100)
                                    ? ThemeReflector.statusColor(context,
                                        status: StatusType.error)
                                    : ThemeReflector.textColor(context,
                                        importance: TextImportance.secondary),
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Minimum price gap between Extra Orders - Hide for Price bot
                      _buildSection(
                        title: 'Minimum price gap between Extra Orders',
                        child: _buildSliderWithValue(
                          value: minimumPriceGap,
                          onChanged: (value) {
                            safeSetState(() {
                              minimumPriceGap = value;
                            });
                          },
                          displayValue:
                              '${minimumPriceGap.toStringAsFixed(1)}%',
                          min: 0.5,
                          max: 10,
                        ),
                      ),
                    ],

                    // Trading Frequency
                    _buildSection(
                      title: 'Trading Frequency',
                      child: _buildTradingFrequencySelector(),
                    ),

                    // Trade Extended Hour
                    _buildSection(
                      title: 'Trade Extended Hour',
                      child: Switch(
                        value: tradeExtendedHour,
                        onChanged: (value) {
                          safeSetState(() {
                            tradeExtendedHour = value;
                          });
                        },
                        activeColor: Theme.of(context).colorScheme.primary,
                        activeTrackColor: Theme.of(context)
                            .colorScheme
                            .primary
                            .withOpacity(0.5),
                      ),
                      isSwitch: true,
                    ),
                  ], // Close else clause
                ],
              ),
            ),
          ),

          // Bottom next button
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: ThemeReflector.surfaceColor(context),
              border: Border(
                top: BorderSide(
                  color: ThemeReflector.dividerColor(context),
                  width: 1,
                ),
              ),
              boxShadow: ThemeReflector.cardShadow(context),
            ),
            child: SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: () {
                  // Validate and save trading parameters
                  if (baseOrderLimit + extraOrders > 100) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Total allocation (Base Order: ${baseOrderLimit.toInt()}% + Extra Orders: ${extraOrders.toInt()}%) exceeds 100%. Please reduce one or both values.',
                        ),
                        backgroundColor: ThemeReflector.statusColor(context,
                            status: StatusType.error),
                        duration: const Duration(seconds: 5),
                      ),
                    );
                    return;
                  }

                  // Validate allocation against available balance
                  final String cleanAmount = _fundAllocationController.text
                      .replaceAll('US\$', '')
                      .replaceAll('\$', '')
                      .replaceAll(',', '')
                      .trim();
                  final double requestedAmount =
                      double.tryParse(cleanAmount) ?? 0.0;

                  // Remove local fund validation - let backend handle this

                  // Save trading parameters and navigate to next screen
                  _saveTradingParameters();

                  // Navigate to Entry screen with all route arguments
                  Navigator.pushNamed(context, AppConstants.entryRoute,
                      arguments: {
                        // Pass through all previous route args
                        ...?routeArgs,
                        // Override specific values
                        'isEditing': isEditing,
                        'botId': botId,
                        'botType': botType
                      });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Next',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    final colorScheme = Theme.of(context).colorScheme;

    // Choose image based on bot type
    String imagePath;
    switch (botType.toLowerCase()) {
      case 'grid':
        imagePath = 'assets/images/grid_trade_parameter.png';
        break;
      case 'momentum':
        imagePath = 'assets/images/momentum_trade_parameter.png';
        break;
      default:
        imagePath = 'assets/images/trading_parameter.png';
        break;
    }

    return Center(
      child: Column(
        children: [
          const SizedBox(height: 16),
          Image.asset(
            imagePath,
            width: 120,
            height: 120,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.show_chart,
                      size: 48,
                      color: colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Trade Parameters",
                      style: TextStyle(
                        color: ThemeReflector.textColor(
                          context,
                          importance: TextImportance.primary,
                        ),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                "Trade Parameter",
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.primary,
                  ),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Step indicator
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required Widget child,
    bool isSwitch = false,
  }) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeReflector.screenBackground(context),
        border: Border(
          bottom: BorderSide(
            color: ThemeReflector.dividerColor(context),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: isSwitch
                ? MainAxisAlignment.spaceBetween
                : MainAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.secondary,
                      ),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 7),
                  Container(
                    width: 16,
                    height: 16,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.info_outline,
                      color: ThemeReflector.iconColor(context, opacity: 0.6),
                      size: 16,
                    ),
                  ),
                ],
              ),
              if (isSwitch) child,
            ],
          ),
          if (!isSwitch) const SizedBox(height: 10),
          if (!isSwitch) child,
        ],
      ),
    );
  }

  Widget _buildRadioGroup({
    required String value,
    required List<String> options,
    required Function(String?) onChanged,
  }) {
    final theme = Theme.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: options.map((option) {
        return Row(
          children: [
            Radio<String>(
              value: option,
              groupValue: value,
              onChanged: onChanged,
              activeColor: theme.colorScheme.primary,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            GestureDetector(
              onTap: () => onChanged(option),
              child: Text(
                option,
                style: TextStyle(
                  color: value == option
                      ? ThemeReflector.textColor(context,
                          importance: TextImportance.primary)
                      : ThemeReflector.textColor(context,
                          importance: TextImportance.secondary),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildSliderWithValue({
    required double value,
    required ValueChanged<double> onChanged,
    required String displayValue,
    double min = 0.1,
    double max = 100,
  }) {
    final theme = Theme.of(context);

    // Đảm bảo giá trị nằm trong khoảng min và max
    double safeValue = value;
    if (safeValue < min) safeValue = min;
    if (safeValue > max) safeValue = max;

    // Prevent infinite precision issues
    safeValue = double.parse(safeValue.toStringAsFixed(2));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: SliderTheme(
                data: SliderThemeData(
                  trackHeight: 8,
                  thumbColor: theme.colorScheme.primary,
                  activeTrackColor: theme.colorScheme.primary,
                  inactiveTrackColor:
                      theme.colorScheme.primary.withOpacity(0.2),
                  overlayColor: theme.colorScheme.primary.withOpacity(0.2),
                ),
                child: Slider(
                  value: safeValue,
                  min: min,
                  max: max,
                  onChanged: onChanged,
                ),
              ),
            ),
            Container(
              width: 90,
              height: 48,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              decoration: BoxDecoration(
                border: Border.all(
                  color: ThemeReflector.borderColor(context),
                ),
                borderRadius: BorderRadius.circular(8),
                color: ThemeReflector.surfaceColor(context),
              ),
              child: Center(
                child: Text(
                  displayValue,
                  style: TextStyle(
                    color: ThemeReflector.textColor(context,
                        importance: TextImportance.primary),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTradingFrequencySelector() {
    final frequencies = ['1m', '5m', '15m', '1H', '2H', '4H', '1D'];
    final theme = Theme.of(context);

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: frequencies.map((freq) {
          final isSelected = tradingFrequency == freq;

          return GestureDetector(
            onTap: () {
              safeSetState(() {
                tradingFrequency = freq;
              });
            },
            child: Container(
              width: 40,
              height: 40,
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                color: isSelected
                    ? theme.colorScheme.primary
                    : ThemeReflector.surfaceColor(context),
                border: Border.all(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : ThemeReflector.borderColor(context),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  freq,
                  style: TextStyle(
                    color: isSelected
                        ? Theme.of(context).colorScheme.onPrimary
                        : ThemeReflector.textColor(context,
                            importance: TextImportance.secondary),
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  // Method to save trading parameters
  void _saveTradingParameters() {
    // Safety check - ensure values don't exceed maximum allowed percentages
    if (baseOrderLimit > 100) {
      baseOrderLimit = 100;
    }

    if (extraOrders > 100) {
      extraOrders = 100;
    }

    if (baseOrderLimit + extraOrders > 100) {
      // Adjust extraOrders to ensure the sum doesn't exceed 100%
      extraOrders = 100 - baseOrderLimit;
    }

    // Get the BotConfigProvider
    final botConfigProvider =
        Provider.of<BotConfigProvider>(context, listen: false);

    // Get the current bot configuration
    final currentConfig = botConfigProvider.botConfig;

    // Create a new BotSummary with updated fund allocation and frequency
    final updatedSummary = BotSummary(
      botName: currentConfig.summary.botName,
      brokerage: currentConfig.summary.brokerage,
      tradingPair: currentConfig.summary.tradingPair,
      frequency: tradingFrequency,
      fundAllocation: _fundAllocationController.text,
      tradeExtendedHour: tradeExtendedHour,
      strategy: strategy, // Use the strategy from route arguments
    );

    // Create updated entries with base order type and percentage
    final updatedEntries = BotEntries(
      orderType: entryOrderType,
      baseOrderLimit:
          'US \$${_calculateDollarAmount(baseOrderLimit)} (${baseOrderLimit.toInt()}%)',
      baseOrderType: baseOrderType,
      daisyChain: currentConfig.entries.daisyChain,
      tradingView: currentConfig.entries.tradingView,
      entryIndicators: currentConfig.entries.entryIndicators,
      minIndicatorsRequired: currentConfig.entries.minIndicatorsRequired,
    );

    // Create updated subsequent orders with extra orders percentage
    final updatedSubsequentOrders = BotSubsequentOrders(
      orderType: currentConfig.subsequentOrders.orderType,
      extraOrders:
          'US\$ ${_calculateDollarAmount(extraOrders)} (${extraOrders.toInt()}%)',
      number: currentConfig.subsequentOrders.number,
      minPriceGap: '$minimumPriceGap%',
      // Add Grid-specific parameters if bot type is grid
      gridUpperRange: botType.toLowerCase() == 'grid'
          ? upperRangeSellPriceLimit.toString()
          : null,
      gridLowerRange: botType.toLowerCase() == 'grid'
          ? lowerRangeBuyPriceLimit.toString()
          : null,
      gridOrderNum: botType.toLowerCase() == 'grid'
          ? numberOfOrdersBetweenMidPriceAndRangeLimit.toString()
          : null,
      gridOrderVol:
          botType.toLowerCase() == 'grid' ? perOrderAmount.toString() : null,
      // Add all trade parameter inputs
      fundAllocation: _fundAllocationController.text,
      orderTypeEntry: entryOrderType.toLowerCase(),
      orderTypeExit: exitOrderType.toLowerCase(),
      baseOrderType: baseOrderType.toLowerCase(),
      extendedTradingHour: tradeExtendedHour,
      tradingFrequency: tradingFrequency,
    );

    // Update the bot configuration with the new values
    botConfigProvider.updateSummary(updatedSummary);
    botConfigProvider.updateEntries(updatedEntries);
    botConfigProvider.updateSubsequentOrders(updatedSubsequentOrders);

    // Log the values for debugging
    print('Fund Allocation: ${_fundAllocationController.text}');
    print('Entry Order Type: $entryOrderType');
    print('Exit Order Type: $exitOrderType');
    print('Base Order Type: $baseOrderType');
    print('Base Order Limit: $baseOrderLimit%');
    print('Extra Orders: $extraOrders%');
    print('Minimum Price Gap: $minimumPriceGap%');
    print('Trading Frequency: $tradingFrequency');
    print('Trade Extended Hour: $tradeExtendedHour');
  }

  // Helper method to calculate dollar amount based on percentage
  String _calculateDollarAmount(double percentage) {
    // Ensure percentage is properly capped
    if (percentage > 100) percentage = 100;
    if (percentage < 0) percentage = 0;

    // Remove currency symbols and commas to get clean number
    String cleanAmount = _fundAllocationController.text
        .replaceAll('US\$', '')
        .replaceAll('\$', '')
        .replaceAll(',', '')
        .replaceAll(' ', '')
        .trim();

    // Parse the fund allocation and calculate the amount
    double fundAmount = double.tryParse(cleanAmount) ?? 1000.0;
    double amount = fundAmount * (percentage / 100);

    // Format with comma for thousands
    return amount.toStringAsFixed(0);
  }

  // Method to validate and cap values
  void _validateAndCapValues() {
    // Cap individual values
    if (baseOrderLimit > 100) baseOrderLimit = 100;
    if (baseOrderLimit < 0.1) baseOrderLimit = 0.1;
    if (extraOrders > 100) extraOrders = 100;
    if (extraOrders < 0.1) extraOrders = 0.1;

    // Kiểm tra minimumPriceGap
    if (minimumPriceGap < 0.5) minimumPriceGap = 0.5;
    if (minimumPriceGap > 10) minimumPriceGap = 10;

    // Ensure total doesn't exceed 100% - but allow exactly 100%
    if (baseOrderLimit + extraOrders > 100) {
      // Only adjust if the sum is actually greater than 100, not equal to 100
      double total = baseOrderLimit + extraOrders;
      if (total > 100.01) {
        // Use small tolerance to avoid floating point precision issues
        // If already adjusting one would make the other negative, distribute values proportionally
        if (baseOrderLimit > 50 && extraOrders > 50) {
          double ratio = baseOrderLimit / total;
          baseOrderLimit = 100 * ratio;
          extraOrders = 100 - baseOrderLimit;
        } else if (baseOrderLimit >= extraOrders) {
          extraOrders = 100 - baseOrderLimit;
          if (extraOrders < 0) extraOrders = 0;
        } else {
          baseOrderLimit = 100 - extraOrders;
          if (baseOrderLimit < 0) baseOrderLimit = 0;
        }
      }
    }
  }

  // Balance validation removed - handled by backend

  // Phương thức kiểm tra tất cả giá trị slider
  void _verifyAllSliderValues() {
    print("SLIDER VALUES VERIFICATION:");
    print("baseOrderLimit: $baseOrderLimit (should be >= 0.1 and <= 100)");
    print("extraOrders: $extraOrders (should be >= 0.1 and <= 100)");
    print("minimumPriceGap: $minimumPriceGap (should be >= 0.5 and <= 10)");

    bool hasIssues = false;
    if (baseOrderLimit < 0.1 || baseOrderLimit > 100) {
      print("WARNING: Invalid baseOrderLimit value!");
      hasIssues = true;
    }
    if (extraOrders < 0.1 || extraOrders > 100) {
      print("WARNING: Invalid extraOrders value!");
      hasIssues = true;
    }
    if (minimumPriceGap < 0.5 || minimumPriceGap > 10) {
      print("WARNING: Invalid minimumPriceGap value!");
      hasIssues = true;
    }

    if (hasIssues) {
      print("FIXING INVALID VALUES...");
      safeSetState(() {
        if (baseOrderLimit < 0.1) baseOrderLimit = 0.1;
        if (baseOrderLimit > 100) baseOrderLimit = 100;
        if (extraOrders < 0.1) extraOrders = 0.1;
        if (extraOrders > 100) extraOrders = 100;
        if (minimumPriceGap < 0.5) minimumPriceGap = 0.5;
        if (minimumPriceGap > 10) minimumPriceGap = 10;
      });
      print("FIXED VALUES:");
      print("baseOrderLimit: $baseOrderLimit");
      print("extraOrders: $extraOrders");
      print("minimumPriceGap: $minimumPriceGap");
    } else {
      print("All slider values are within valid ranges.");
    }
  }

  // Get default parameters based on bot type
  Map<String, dynamic> _getDefaultParametersForBotType(String botType) {
    switch (botType.toLowerCase()) {
      case 'quickstart':
      case 'quick_start':
        return {
          'baseOrderPercentage': 50.0, // base_order_percentage từ payload
          'extraOrderPercentage': 0.0, // extra_order_percentage từ payload
          'minPriceGap': 0.0, // extra_price_gap_min từ payload
          'initialFund': 0.0, // fund allocation để 0
          'frequency': '15m', // frequency: 15 từ payload
          'baseOrderType': 'static', // base_order_type từ payload
          'orderTypeEntry': 'market', // order_type từ payload
          'orderTypeExit': 'market', // order_type_exit từ payload
          'extendedTradingHour': false, // extended_trading_hour: 0 từ payload
        };
      case 'simple':
        return {
          'baseOrderPercentage': 50.0,
          'extraOrderPercentage': 0.0,
          'minPriceGap': 0.0,
          'initialFund': 0.0,
          'frequency': '15m',
          'baseOrderType': 'static',
          'orderTypeEntry': 'market',
          'orderTypeExit': 'market',
          'extendedTradingHour': false,
        };
      case 'dca':
        return {
          'baseOrderPercentage': 30.0,
          'extraOrderPercentage': 35.0,
          'minPriceGap': 3.0,
          'initialFund': 0.0, // Fund allocation để 0
          'frequency': '30m',
          'baseOrderType': 'static',
          'orderTypeEntry': 'limit',
          'orderTypeExit': 'limit',
          'extendedTradingHour': true,
        };
      case 'price':
        return {
          'baseOrderPercentage': 50.0, // base_order_percentage từ payload
          'extraOrderPercentage': 25.0, // extra_order_percentage từ payload
          'minPriceGap': 0.0, // extra_price_gap_min từ payload
          'initialFund': 0.0, // fund allocation để 0
          'frequency': '15m', // frequency: 15 từ payload
          'baseOrderType': 'static', // base_order_type từ payload
          'orderTypeEntry': 'market', // order_type từ payload
          'orderTypeExit': 'market', // order_type_exit từ payload
          'extendedTradingHour': false, // extended_trading_hour: 0 từ payload
          // Thêm các tham số đặc biệt cho price bot
          'entryPrice': '1', // entry_price từ payload
          'profit': '1', // profit từ payload
          'stopLoss': 0, // stop_loss từ payload
          'indicatorTriggersExit': 0, // indicator_triggers_exit từ payload
        };
      case 'grid':
        return {
          'baseOrderPercentage': 25.0,
          'extraOrderPercentage': 40.0,
          'minPriceGap': 4.0,
          'initialFund': 0.0, // Fund allocation để 0
          'frequency': '1h',
          'baseOrderType': 'static',
          'orderTypeEntry': 'limit',
          'orderTypeExit': 'limit',
          'extendedTradingHour': false,
        };
      case 'momentum':
        return {
          'baseOrderPercentage': 50.0, // base_order_percentage từ payload
          'extraOrderPercentage': 25.0, // extra_order_percentage từ payload
          'minPriceGap': 2.0, // extra_price_gap_min từ payload
          'initialFund': 0.0, // Fund allocation để 0
          'frequency': '15m', // frequency: 15 từ payload
          'baseOrderType': 'static', // base_order_type từ payload
          'orderTypeEntry': 'market', // order_type từ payload
          'orderTypeExit': 'market', // order_type_exit từ payload
          'extendedTradingHour': false, // extended_trading_hour: 0 từ payload
          // Thêm các tham số đặc biệt cho momentum bot
          'basePriceGapMin': 1, // base_price_gap_min từ payload
          'extraPriceGapMin': 2, // extra_price_gap_min từ payload
        };
      case 'sell':
        return {
          'baseOrderPercentage': 50.0, // base_order_percentage từ payload
          'extraOrderPercentage': 25.0, // extra_order_percentage từ payload
          'minPriceGap': 0.0, // extra_price_gap_min từ payload
          'initialFund': 1.0, // initial_fund từ payload
          'frequency': '15m', // frequency: 15 từ payload (15 minutes)
          'baseOrderType': 'static', // base_order_type từ payload
          'orderTypeEntry': 'market', // order_type từ payload
          'orderTypeExit': 'market', // order_type_exit từ payload
          'extendedTradingHour': false, // extended_trading_hour: 0 từ payload
          // Thêm các tham số đặc biệt cho sell bot
          'avgEntryPrice': 0, // avg_entry_price từ payload
          'sellPrice': 0, // sell_price từ payload
          'minTp': 0, // min_tp từ payload
        };
      case 'advanced':
      default:
        // Giữ nguyên tham số advanced như yêu cầu
        return {
          'baseOrderPercentage': 50.0,
          'extraOrderPercentage': 25.0,
          'minPriceGap': 2.5,
          'initialFund': 0.0, // Fund allocation để 0
          'frequency': '15m',
          'baseOrderType': 'static',
          'orderTypeEntry': 'market',
          'orderTypeExit': 'market',
          'extendedTradingHour': true,
        };
    }
  }

  // Build Quickstart-specific Trade Parameter UI (simplified)
  List<Widget> _buildQuickstartTradeParameterUI() {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return [
      // Fund Allocation
      _buildSection(
        title: 'Fund Allocation',
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextField(
              controller: _fundAllocationController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: ThemeReflector.borderColor(context),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: ThemeReflector.borderColor(context),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: theme.colorScheme.primary,
                  ),
                ),
                hintText: 'Enter amount',
                helperText: 'Enter fund allocation amount',
              ),
              onChanged: (_) => safeSetState(() {}),
            ),
            const SizedBox(height: 10),
            const SizedBox(height: 8),
            if (baseAsset.isNotEmpty && quoteAsset.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'Trading $baseAsset/$quoteAsset',
                  style: textTheme.bodySmall,
                ),
              ),
          ],
        ),
      ),

      // Trade Extended Hour
      _buildSection(
        title: 'Trade Extended Hour',
        child: Switch(
          value: tradeExtendedHour,
          onChanged: (value) {
            safeSetState(() {
              tradeExtendedHour = value;
            });
          },
          activeColor: Theme.of(context).colorScheme.primary,
          activeTrackColor:
              Theme.of(context).colorScheme.primary.withOpacity(0.5),
        ),
        isSwitch: true,
      ),
    ];
  }

  // Build Grid-specific Trade Parameter UI
  List<Widget> _buildGridTradeParameterUI() {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return [
      // Upper Range Sell Price Limit % above Mid Price
      _buildSection(
        title: 'Upper Range Sell Price Limit % above Mid Price',
        child: TextField(
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: ThemeReflector.borderColor(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: ThemeReflector.borderColor(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.colorScheme.primary),
            ),
            hintText: '0%',
          ),
          onChanged: (value) {
            safeSetState(() {
              upperRangeSellPriceLimit = double.tryParse(value) ?? 0.0;
            });
          },
        ),
      ),

      // Lower Range Buy Price Limit % below Mid Price
      _buildSection(
        title: 'Lower Range Buy Price Limit % below Mid Price',
        child: TextField(
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: ThemeReflector.borderColor(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: ThemeReflector.borderColor(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.colorScheme.primary),
            ),
            hintText: '0%',
          ),
          onChanged: (value) {
            safeSetState(() {
              lowerRangeBuyPriceLimit = double.tryParse(value) ?? 0.0;
            });
          },
        ),
      ),

      // No. of Orders Between Mid Price and Range Limit
      _buildSection(
        title: 'No. of Orders Between Mid Price and Range Limit',
        child: TextField(
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: ThemeReflector.borderColor(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: ThemeReflector.borderColor(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.colorScheme.primary),
            ),
            hintText: '0 orders',
          ),
          onChanged: (value) {
            safeSetState(() {
              numberOfOrdersBetweenMidPriceAndRangeLimit =
                  int.tryParse(value) ?? 0;
            });
          },
        ),
      ),

      // Per Order Amount
      _buildSection(
        title: 'Per Order Amount',
        child: TextField(
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: ThemeReflector.borderColor(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: ThemeReflector.borderColor(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.colorScheme.primary),
            ),
            hintText: '0 USD',
          ),
          onChanged: (value) {
            safeSetState(() {
              perOrderAmount = double.tryParse(value) ?? 0.0;
            });
          },
        ),
      ),

      // Estimated Total Fund
      _buildSection(
        title:
            'Estimated Total Fund: ${estimatedTotalFund.toStringAsFixed(0)} USD',
        child: Container(),
      ),

      // Total Fund Required
      _buildSection(
        title: 'Total Fund Required',
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Buy-side: ${totalFundRequiredBuySide.toStringAsFixed(0)} USD',
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: ThemeReflector.textColor(context,
                    importance: TextImportance.primary),
              ),
            ),
            Text(
              'Fund Available: ${fundAvailableBuySide.toStringAsFixed(1)} USD',
              style: textTheme.bodyMedium?.copyWith(
                color: ThemeReflector.textColor(context,
                    importance: TextImportance.secondary),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Sell-side: ${totalFundRequiredSellSide.toStringAsFixed(0)} AAPL',
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: ThemeReflector.textColor(context,
                    importance: TextImportance.primary),
              ),
            ),
            Text(
              'Fund Available: ${fundAvailableSellSide.toStringAsFixed(2)} AAPL',
              style: textTheme.bodyMedium?.copyWith(
                color: ThemeReflector.textColor(context,
                    importance: TextImportance.secondary),
              ),
            ),
          ],
        ),
      ),
    ];
  }
}
