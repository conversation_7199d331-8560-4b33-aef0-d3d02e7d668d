import 'bot_payload_builder.dart';

/// Price bot specific payload builder
class PriceBotPayloadBuilder extends BotPayloadBuilder {
  PriceBotPayloadBuilder({
    required super.botConfig,
    required super.exchangePairId,
    required super.exchangeId,
    required super.strategy,
    required super.isEditMode,
    super.botId,
  });

  @override
  String get botType => 'price';

  @override
  Map<String, dynamic> buildPayload() {
    // Parse user inputs - LẤY TẤT CẢ TỪ USER INPUT
    final initialFund = _getInitialFund();
    final baseOrderPercentage = _getBaseOrderPercentage();
    final extraOrderPercentage = _getExtraOrderPercentage();
    final takeProfit = _getTakeProfit();
    final stopLoss = _getStopLoss();
    final minPriceGap = _getMinPriceGap();
    final frequency = getFrequencyValue();
    final extendedTradingHour = botConfig.summary.tradeExtendedHour ? 1 : 0;

    // Base payload structure for price bot - TẤT CẢ TỪ USER INPUT
    Map<String, dynamic> payload = {
      'apply_to_current_deal': false,
      'avg_entry_price': initialFund, // Từ user fund allocation
      'back_test_time_frame': 365,
      'base_order_percentage': baseOrderPercentage, // Từ user input
      'base_order_type': getBaseOrderTypeValue(), // Từ user input
      'base_price_gap_min': minPriceGap.toString(), // Từ user input
      'bot_indicators': formatBotIndicators(botConfig), // Từ user input
      'daisy_chain': 0,
      'dca_entry_type': "time",
      'dca_value': "daily",
      'dca_value2': "1",
      'dca_value3': 0,
      'disable_after_deal_closed': 0,
      'end_day_auto_close': 0,
      'entry_price': initialFund.toString(), // Từ user fund allocation
      'exchange_id': exchangeId,
      'exchange_pair_id': exchangePairId,
      'extended_trading_hour': extendedTradingHour, // Từ user input
      'extra_order_percentage': extraOrderPercentage, // Từ user input
      'extra_price_gap_min': minPriceGap, // Từ user input
      'first_in_daisy_chain': 0,
      'fractional_trading_disabled': 0,
      'frequency': frequency, // Từ user input
      'grid_entry_type': "imd",
      'grid_exit_outer_range': 0,
      'grid_exit_strategy_has_trade': "cancel_restart",
      'grid_exit_strategy_no_trade': "cancel_restart",
      'grid_exit_wait_time': 0,
      'grid_lower_range': "",
      'grid_order_num': "",
      'grid_order_vol': "",
      'grid_range_kline_direction': 0,
      'grid_upper_range': "",
      'id': isEditMode ? botId : null,
      'indicator_triggers_entry': _getIndicatorTriggersEntry(), // Từ user input
      'indicator_triggers_exit': _getIndicatorTriggersExit(), // Từ user input
      'initial_fund': initialFund.toString(), // Từ user fund allocation
      'min_tp': initialFund, // Từ user fund allocation
      'name': botConfig.summary.botName.isNotEmpty
          ? botConfig.summary.botName
          : "New Bot", // Từ user input
      'off_after_close_deal': 0,
      'order_filled_email_enabled': 1,
      'order_type':
          getOrderTypeValue(botConfig.entries.orderType), // Từ user input
      'order_type_exit': getOrderTypeValue(
          botConfig.exits.orderType ?? 'Market'), // Từ user input
      'profit': takeProfit.toString(), // Từ user input
      'sell_price': initialFund, // Từ user fund allocation
      'stop_loss': stopLoss.toString(), // Từ user input
      'strategy': strategy, // Từ user selection
      'tps': [],
      'trading_hour_247': 0,
      'tsls': [],
      'type': "price",
    };

    return payload;
  }

  /// Get initial fund with validation
  double _getInitialFund() {
    final initialFund = parseInitialFund(botConfig.summary.fundAllocation);
    return initialFund > 0 ? initialFund : 1000.0; // Default 1000
  }

  /// Get base order percentage from user input
  double _getBaseOrderPercentage() {
    // Parse từ entries.baseOrderLimit như "US $500 (50%)" -> 50.0
    final baseOrderLimit = botConfig.entries.baseOrderLimit;

    // Nếu user chưa nhập gì (default value), trả về 0
    if (baseOrderLimit.isEmpty || baseOrderLimit == 'US \$500 (50%)') {
      print('Price bot: Base order percentage not set by user, using 0');
      return 0.0;
    }

    if (baseOrderLimit.contains('(') && baseOrderLimit.contains(')')) {
      final regex = RegExp(r'\((\d+\.?\d*)%\)');
      final match = regex.firstMatch(baseOrderLimit);
      if (match != null && match.groupCount >= 1) {
        final value = double.tryParse(match.group(1) ?? '0') ?? 0.0;
        print(
            'Price bot: Parsed base order percentage from user input: $value%');
        return value;
      }
    }

    print('Price bot: Could not parse base order percentage, using 0');
    return 0.0; // User chưa nhập, trả về 0
  }

  /// Get extra order percentage from user input
  double _getExtraOrderPercentage() {
    // Parse từ subsequentOrders.extraOrders như "US$ 250 (25%)" -> 25.0
    final extraOrders = botConfig.subsequentOrders.extraOrders;

    // Nếu user chưa nhập gì (default value), trả về 0
    if (extraOrders.isEmpty || extraOrders == 'US\$ 250 (25%)') {
      print('Price bot: Extra order percentage not set by user, using 0');
      return 0.0;
    }

    if (extraOrders.contains('(') && extraOrders.contains(')')) {
      final regex = RegExp(r'\((\d+\.?\d*)%\)');
      final match = regex.firstMatch(extraOrders);
      if (match != null && match.groupCount >= 1) {
        final value = double.tryParse(match.group(1) ?? '0') ?? 0.0;
        print(
            'Price bot: Parsed extra order percentage from user input: $value%');
        return value;
      }
    }

    print('Price bot: Could not parse extra order percentage, using 0');
    return 0.0; // User chưa nhập, trả về 0
  }

  /// Get take profit from user input
  double _getTakeProfit() {
    // Parse từ exits.takeProfit như "5.0%" -> 5.0
    final takeProfit = botConfig.exits.takeProfit;

    // Nếu user chưa nhập gì (default value), trả về 0
    if (takeProfit.isEmpty || takeProfit == '5%') {
      print('Price bot: Take profit not set by user, using 0');
      return 0.0;
    }

    final regex = RegExp(r'(\d+\.?\d*)');
    final match = regex.firstMatch(takeProfit);
    if (match != null) {
      final value = double.tryParse(match.group(1) ?? '0') ?? 0.0;
      print('Price bot: Parsed take profit from user input: $value%');
      return value;
    }

    print('Price bot: Could not parse take profit, using 0');
    return 0.0; // User chưa nhập, trả về 0
  }

  /// Get stop loss from user input
  double _getStopLoss() {
    // Parse từ exits.stopLoss như "2.0%" -> 2.0
    final stopLoss = botConfig.exits.stopLoss ?? '0';

    // Nếu user chưa nhập gì (default value), trả về 0
    if (stopLoss.isEmpty || stopLoss == '0' || stopLoss == '2%') {
      print('Price bot: Stop loss not set by user, using 0');
      return 0.0;
    }

    final regex = RegExp(r'(\d+\.?\d*)');
    final match = regex.firstMatch(stopLoss);
    if (match != null) {
      final value = double.tryParse(match.group(1) ?? '0') ?? 0.0;
      print('Price bot: Parsed stop loss from user input: $value%');
      return value;
    }

    print('Price bot: Could not parse stop loss, using 0');
    return 0.0; // User chưa nhập, trả về 0
  }

  /// Get min price gap from user input
  double _getMinPriceGap() {
    // Parse từ subsequentOrders.minPriceGap như "2.5 %" -> 2.5
    final minPriceGap = botConfig.subsequentOrders.minPriceGap;

    // Nếu user chưa nhập gì (default value), trả về 0
    if (minPriceGap.isEmpty || minPriceGap == '2.5 %') {
      print('Price bot: Min price gap not set by user, using 0');
      return 0.0;
    }

    final regex = RegExp(r'(\d+\.?\d*)');
    final match = regex.firstMatch(minPriceGap);
    if (match != null) {
      final value = double.tryParse(match.group(1) ?? '0') ?? 0.0;
      print('Price bot: Parsed min price gap from user input: $value%');
      return value;
    }

    print('Price bot: Could not parse min price gap, using 0');
    return 0.0; // User chưa nhập, trả về 0
  }

  /// Get indicator triggers entry from user input
  int _getIndicatorTriggersEntry() {
    // Parse từ entries.minIndicatorsRequired như "1 out of 2 Indicators" -> 1
    final minIndicators = botConfig.entries.minIndicatorsRequired;
    final regex = RegExp(r'(\d+)');
    final match = regex.firstMatch(minIndicators);
    if (match != null) {
      return int.tryParse(match.group(1) ?? '0') ?? 0;
    }
    return 0; // Default fallback
  }

  /// Get indicator triggers exit from user input
  int _getIndicatorTriggersExit() {
    // Parse từ exits.minIndicatorsRequired như "2 out of 2 Indicators" -> 2
    final minIndicators = botConfig.exits.minIndicatorsRequired;
    final regex = RegExp(r'(\d+)');
    final match = regex.firstMatch(minIndicators);
    if (match != null) {
      return int.tryParse(match.group(1) ?? '0') ?? 0;
    }
    return 0; // Default fallback
  }
}
