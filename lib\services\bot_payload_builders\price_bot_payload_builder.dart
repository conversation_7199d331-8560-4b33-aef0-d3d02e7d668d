import 'bot_payload_builder.dart';

/// Price bot specific payload builder
class PriceBotPayloadBuilder extends BotPayloadBuilder {
  PriceBotPayloadBuilder({
    required super.botConfig,
    required super.exchangePairId,
    required super.exchangeId,
    required super.strategy,
    required super.isEditMode,
    super.botId,
  });

  @override
  String get botType => 'price';

  @override
  Map<String, dynamic> buildPayload() {
    // Parse user inputs
    final initialFund = _getInitialFund();

    // Base payload structure for price bot theo mẫu user cung cấp
    Map<String, dynamic> payload = {
      'apply_to_current_deal': false,
      'avg_entry_price': 0,
      'back_test_time_frame': 365,
      'base_order_percentage': 50,
      'base_order_type': "static",
      'base_price_gap_min': "",
      'bot_indicators': [],
      'daisy_chain': 0,
      'dca_entry_type': "time",
      'dca_value': "daily",
      'dca_value2': "1",
      'dca_value3': 0,
      'disable_after_deal_closed': 0,
      'end_day_auto_close': 0,
      'entry_price': "1",
      'exchange_id': exchangeId,
      'exchange_pair_id': exchangePairId,
      'extended_trading_hour': 0,
      'extra_order_percentage': 25,
      'extra_price_gap_min': 0,
      'first_in_daisy_chain': 0,
      'fractional_trading_disabled': 0,
      'frequency': 15,
      'grid_entry_type': "imd",
      'grid_exit_outer_range': 0,
      'grid_exit_strategy_has_trade': "cancel_restart",
      'grid_exit_strategy_no_trade': "cancel_restart",
      'grid_exit_wait_time': 0,
      'grid_lower_range': "",
      'grid_order_num': "",
      'grid_order_vol': "",
      'grid_range_kline_direction': 0,
      'grid_upper_range': "",
      'id': isEditMode ? botId : null,
      'indicator_triggers_exit': 0,
      'initial_fund':
          initialFund.toString(), // Sử dụng fund allocation thực tế từ user
      'min_tp': 0,
      'name': botConfig.summary.botName.isNotEmpty
          ? botConfig.summary.botName
          : "New Bot",
      'off_after_close_deal': 0,
      'order_filled_email_enabled': 1,
      'order_type': "market",
      'order_type_exit': "market",
      'profit': "2",
      'sell_price': 0,
      'stop_loss': 0,
      'strategy': strategy,
      'tps': [],
      'trading_hour_247': 0,
      'tsls': [],
      'type': "price",
    };

    return payload;
  }

  /// Get initial fund with validation
  double _getInitialFund() {
    final initialFund = parseInitialFund(botConfig.summary.fundAllocation);
    return initialFund > 0 ? initialFund : 1000.0; // Default 1000
  }
}
