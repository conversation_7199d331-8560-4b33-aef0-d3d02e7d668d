import 'bot_payload_builder.dart';

/// Price bot specific payload builder
class PriceBotPayloadBuilder extends BotPayloadBuilder {
  PriceBotPayloadBuilder({
    required super.botConfig,
    required super.exchangePairId,
    required super.exchangeId,
    required super.strategy,
    required super.isEditMode,
    super.botId,
  });

  @override
  String get botType => 'price';

  @override
  Map<String, dynamic> buildPayload() {
    // Parse user inputs from BotConfig
    final initialFund = _getInitialFund();
    final takeProfit = _getPriceTakeProfit(); // ✅ Get as string for Price bot
    final stopLoss = _getPriceStopLoss(); // ✅ Get as string for Price bot
    final entryPrice = _getPriceEntryValue();

    // Base payload structure for price bot with user inputs
    Map<String, dynamic> payload = {
      'apply_to_current_deal': false,
      'avg_entry_price': 0,
      'back_test_time_frame': 365,
      'base_order_percentage':
          _getPriceBaseOrderPercentage(), // ✅ From user input
      'base_order_type': _getPriceBaseOrderType(), // ✅ From user input
      'base_price_gap_min': "",
      'bot_indicators': [],
      'daisy_chain': 0,
      'dca_entry_type': "time",
      'dca_value': "daily",
      'dca_value2': "1",
      'dca_value3': 0,
      'disable_after_deal_closed': 0,
      'end_day_auto_close': 0,
      'entry_price': entryPrice, // ✅ From user input
      'exchange_id': exchangeId,
      'exchange_pair_id': exchangePairId,
      'extended_trading_hour':
          botConfig.summary.tradeExtendedHour ? 1 : 0, // ✅ From user input
      'extra_order_percentage':
          _getPriceExtraOrderPercentage(), // ✅ Fixed value (no slider)
      'extra_price_gap_min':
          _getPriceExtraPriceGap(), // ✅ Fixed value (no slider)
      'first_in_daisy_chain': 0,
      'fractional_trading_disabled': 0,
      'frequency': _getPriceFrequency(), // ✅ From user input
      'grid_entry_type': "imd",
      'grid_exit_outer_range': 0,
      'grid_exit_strategy_has_trade': "cancel_restart",
      'grid_exit_strategy_no_trade': "cancel_restart",
      'grid_exit_wait_time': 0,
      'grid_lower_range': "",
      'grid_order_num': "",
      'grid_order_vol': "",
      'grid_range_kline_direction': 0,
      'grid_upper_range': "",
      'id': isEditMode ? botId : null,
      'indicator_triggers_exit': 0,
      'initial_fund': initialFund.toString(), // ✅ From user input
      'min_tp': 0,
      'name': botConfig.summary.botName, // ✅ From user input
      'off_after_close_deal': 0,
      'order_filled_email_enabled': 1,
      'order_type':
          getOrderTypeValue(botConfig.entries.orderType), // ✅ From user input
      'order_type_exit': _getPriceExitOrderType(), // ✅ From user input
      'profit': takeProfit, // ✅ From user input
      'sell_price': 0,
      'stop_loss': stopLoss, // ✅ From user input
      'strategy': strategy, // ✅ From user input
      'tps': [],
      'trading_hour_247': 0,
      'tsls': [],
      'type': "price",
    };

    // Price bot payload ready

    return payload;
  }

  /// Get initial fund with validation
  double _getInitialFund() {
    final initialFund = parseInitialFund(botConfig.summary.fundAllocation);
    return initialFund > 0 ? initialFund : 1000.0; // Default 1000
  }

  /// Get entry price value from user input
  String _getPriceEntryValue() {
    // Price bot: get entry price from priceEntry field
    final priceEntry = botConfig.entries.priceEntry ?? '';

    if (priceEntry.isNotEmpty) {
      final price = double.tryParse(priceEntry);
      if (price != null && price > 0) {
        return price.toString();
      }
    }

    return "1"; // Default fallback
  }

  /// Get base order percentage for Price bot (from user input)
  double _getPriceBaseOrderPercentage() {
    // Price bot: get from user input in Trade Parameter screen
    final baseOrderPercentage =
        parsePercentageValue(botConfig.entries.baseOrderLimit);
    return baseOrderPercentage > 0 ? baseOrderPercentage : 50.0; // Default 50%
  }

  /// Get base order type for Price bot (from user selection)
  String _getPriceBaseOrderType() {
    // Price bot: use base order type from user selection
    // Price bot needs "static" or "dynamic" (not "percentage" like other bots)
    final originalType = botConfig.entries.baseOrderType;
    final convertedType =
        originalType.toLowerCase() == 'static' ? 'static' : 'dynamic';

    return convertedType;
  }

  /// Get extra order percentage for Price bot (fixed 25% - no slider in UI)
  double _getPriceExtraOrderPercentage() {
    // Price bot: fixed 25% extra order percentage (no slider in UI)
    return 25.0;
  }

  /// Get extra price gap for Price bot (fixed 0 - no slider in UI)
  double _getPriceExtraPriceGap() {
    // Price bot: fixed 0 extra price gap (no slider in UI)
    return 0.0;
  }

  /// Get frequency for Price bot (from user selection)
  int _getPriceFrequency() {
    // Price bot: use frequency from user selection
    return getFrequencyValue();
  }

  /// Get exit order type for Price bot (from user selection)
  String _getPriceExitOrderType() {
    // Price bot: use exit order type from user selection
    // For Price bot, exit order type should be same as entry order type
    return getOrderTypeValue(botConfig.entries.orderType);
  }

  /// Get take profit for Price bot (from user input as string)
  String _getPriceTakeProfit() {
    // Price bot: get profit from "Sell when price is more than or equal to" field
    final priceExitStr = botConfig.exits.priceExit ?? '';

    if (priceExitStr.isNotEmpty) {
      final price = double.tryParse(priceExitStr);
      if (price != null && price > 0) {
        return price.toString();
      }
    }

    return "2"; // Default fallback
  }

  /// Get stop loss for Price bot (from user input as string)
  String _getPriceStopLoss() {
    // Price bot: get stop loss from user input
    final stopLossStr = botConfig.exits.stopLoss ?? '0';

    // Parse percentage value and return as string
    final percentage = parsePercentageValue(stopLossStr);
    final result = percentage.toString();

    return result;
  }
}
