import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:stockhero/models/bot_config.dart';
import 'package:stockhero/services/subscription_validation_service.dart';
import 'package:stockhero/shared/controllers/auth_controller.dart';
import 'package:stockhero/shared/providers/bot_config_provider.dart';
import 'package:stockhero/shared/utils/theme_reflector.dart';
import 'package:stockhero/shared/utils/theme_utils.dart';
import "package:stockhero/utils/constants.dart";

class EntryScreen extends StatefulWidget {
  const EntryScreen({Key? key}) : super(key: key);

  @override
  State<EntryScreen> createState() => _EntryScreenState();
}

class _EntryScreenState extends State<EntryScreen> {
  // Toggle switches
  bool enableDaisyChain = false; // Set mặc định là OFF
  bool integrateTradingView = false;
  bool isFirstInDaisyChain = false;

  // Indicator triggers
  List<Map<String, dynamic>> indicators = [];

  // Minimum indicators required
  double minIndicators = 0.0;

  // Route arguments to pass through
  Map<String, dynamic>? routeArgs;

  // API data for indicators
  List<Map<String, dynamic>> availableIndicators = [];
  bool isLoadingIndicators = false;

  // Bot data
  int? botId;
  bool isEditing = false;
  String? botType;

  // Price bot specific controller
  final TextEditingController _priceController = TextEditingController();

  // Sell bot specific controller
  final TextEditingController _avgEntryPriceController =
      TextEditingController();

  // Grid Entry specific variables
  String selectedEntryType = 'Immediate';
  bool tradingViewEnabled = false;
  double indicatorTriggers = 0.0;
  String selectedTradingFrequency = '15m';
  bool isRangeLesser = true;
  String rangePercentage = '5';
  String rangePeriods = '3';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _processRouteArguments();
      _fetchIndicators();
    });
  }

  void _processRouteArguments() {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      // Store all route args to pass through to next screen
      routeArgs = args;

      setState(() {
        botId = args['botId'];
        isEditing = args['isEditing'] == true;
        botType = args['botType']?.toString();
      });

      // If editing, load existing bot data and preset indicators
      if (isEditing && botId != null) {
        _loadExistingBotData();
      }
    }
  }

  Future<void> _fetchIndicators() async {
    setState(() {
      isLoadingIndicators = true;
    });

    try {
      final authController =
          Provider.of<AuthController>(context, listen: false);
      final token = authController.token;

      if (token == null) {
        print('Authentication token not found');
        setState(() {
          isLoadingIndicators = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse('${AppConstants.apiBaseUrl}/api/indicators'),
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.apiTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['code'] == 200 && data['indicators'] != null) {
          final indicatorsList = data['indicators'] as List;

          setState(() {
            availableIndicators =
                indicatorsList.map((e) => e as Map<String, dynamic>).toList();
            isLoadingIndicators = false;
          });

          print('Loaded ${availableIndicators.length} indicators');
        } else {
          print('Failed to load indicators: ${data['message']}');
          setState(() {
            isLoadingIndicators = false;
          });
        }
      } else {
        print('Failed to load indicators. Status code: ${response.statusCode}');
        setState(() {
          isLoadingIndicators = false;
        });
      }
    } catch (e) {
      print('Exception in _fetchIndicators: $e');
      setState(() {
        isLoadingIndicators = false;
      });
    }
  }

  Future<void> _loadExistingBotData() async {
    if (botId == null) return;

    try {
      // TODO: Load bot data from API using botId
      // For now, we'll load from BotConfigProvider if available
      final botConfigProvider =
          Provider.of<BotConfigProvider>(context, listen: false);
      final config = botConfigProvider.botConfig;

      // Load existing entry settings
      setState(() {
        // Parse daisy chain settings
        if (config.entries.daisyChain.toLowerCase().contains('activated')) {
          enableDaisyChain = true;
          isFirstInDaisyChain =
              config.entries.daisyChain.toLowerCase().contains('first');
        } else {
          enableDaisyChain = false;
          isFirstInDaisyChain = false;
        }

        // Parse TradingView integration
        integrateTradingView = config.entries.tradingView.isNotEmpty;

        // Load existing indicators - this is the key fix for Issue 1
        indicators.clear();
        for (String indicatorStr in config.entries.entryIndicators) {
          // Parse indicator string like "RSI (14, 30, 70)"
          Map<String, dynamic> indicator = _parseIndicatorString(indicatorStr);
          indicators.add(indicator);
        }

        // Parse minimum indicators required
        if (config.entries.minIndicatorsRequired.isNotEmpty) {
          final regex = RegExp(r'(\d+)\s+out\s+of\s+(\d+)');
          final match = regex.firstMatch(config.entries.minIndicatorsRequired);
          if (match != null) {
            minIndicators = double.tryParse(match.group(1) ?? '0') ?? 0.0;
          }
        }
      });

      print('Loaded existing bot data for editing:');
      print('Enable Daisy Chain: $enableDaisyChain');
      print('Is First in Daisy Chain: $isFirstInDaisyChain');
      print('Integrate TradingView: $integrateTradingView');
      print('Loaded ${indicators.length} indicators');
      print('Min Indicators: $minIndicators');
    } catch (e) {
      print('Error loading existing bot data: $e');
    }
  }

  Map<String, dynamic> _parseIndicatorString(String indicatorStr) {
    // Parse strings like "RSI (14, 30, 70)" or "Bollinger Band (20, 2.0)"
    String name = indicatorStr;
    int? periodNum;
    double? value2;
    double? value3;

    if (indicatorStr.contains('(') && indicatorStr.contains(')')) {
      final parts = indicatorStr.split('(');
      name = parts[0].trim();

      final paramStr = parts[1].replaceAll(')', '').trim();
      final params = paramStr.split(',').map((s) => s.trim()).toList();

      if (params.isNotEmpty) {
        periodNum = int.tryParse(params[0]);
      }
      if (params.length > 1) {
        value2 = double.tryParse(params[1]);
      }
      if (params.length > 2) {
        value3 = double.tryParse(params[2]);
      }
    }

    return {
      'name': name,
      'period_num': periodNum ?? 14,
      'value2': value2 ?? 2.0,
      'value3': value3,
      'description': 'Loaded from existing bot configuration',
    };
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: ThemeReflector.screenBackground(context),
      appBar: AppBar(
        backgroundColor: ThemeReflector.surfaceColor(context),
        elevation: 0,
        title: Text(
          'Create Bot',
          style: TextStyle(
            color: ThemeReflector.textColor(
              context,
              importance: TextImportance.primary,
            ),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: ThemeReflector.iconColor(context),
            size: 24,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.close,
              color: ThemeReflector.iconColor(context),
              size: 24,
            ),
            onPressed: () => Navigator.pushNamedAndRemoveUntil(
              context,
              AppConstants.dashboardRoute,
              (route) => false,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Header with image and title
                  _buildHeader(theme, colorScheme, textTheme),

                  // Grid Entry UI
                  if (botType?.toLowerCase() == 'grid')
                    _buildGridEntrySection(theme)
                  // Momentum Entry UI
                  else if (botType?.toLowerCase() == 'momentum')
                    _buildMomentumEntrySection(theme)
                  // Quickstart Entry UI (simplified)
                  else if (botType?.toLowerCase() == 'quickstart')
                    _buildQuickstartEntrySection(theme)
                  else ...[
                    // Price input for price bot type - chỉ hiển thị input field duy nhất
                    if (botType == 'price')
                      _buildPriceInputSection(theme)
                    else ...[
                      // Average Entry Price input for sell bot type
                      if (botType == 'sell')
                        _buildAverageEntryPriceSection(theme),

                      // Enable Daisy Chain
                      _buildToggleSection(
                        theme: theme,
                        title: 'Enable Daisy Chain',
                        value: enableDaisyChain,
                        onChanged: (value) {
                          setState(() {
                            enableDaisyChain = value;
                          });
                        },
                      ),

                      // First in Daisy Chain checkbox
                      if (enableDaisyChain)
                        Padding(
                          padding: const EdgeInsets.only(
                              left: 16.0, right: 16.0, bottom: 16.0),
                          child: Row(
                            children: [
                              Checkbox(
                                value: isFirstInDaisyChain,
                                onChanged: (value) {
                                  setState(() {
                                    isFirstInDaisyChain = value ?? false;
                                  });
                                },
                                activeColor: colorScheme.primary,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                side: BorderSide(
                                  color: ThemeReflector.borderColor(context),
                                  width: 1,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Set up this bot to be the first in the Daisy Chain',
                                  style: TextStyle(
                                    color: ThemeReflector.textColor(
                                      context,
                                      importance: TextImportance.secondary,
                                    ),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                      Divider(
                          height: 1,
                          color: ThemeReflector.dividerColor(context)),

                      // Integrate TradingView
                      _buildToggleSection(
                        theme: theme,
                        title: 'Integrate TradingView',
                        value: integrateTradingView,
                        onChanged: (value) {
                          setState(() {
                            integrateTradingView = value;
                          });
                        },
                        showInfoIcon: true,
                      ),

                      Divider(
                          height: 1,
                          color: ThemeReflector.dividerColor(context)),

                      // Indicator Triggers
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      'Indicator Triggers',
                                      style: TextStyle(
                                        color: ThemeReflector.textColor(
                                          context,
                                          importance: TextImportance.secondary,
                                        ),
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(width: 7),
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.info_outline,
                                        color: ThemeReflector.iconColor(context,
                                            opacity: 0.6),
                                        size: 16,
                                      ),
                                    ),
                                  ],
                                ),
                                // Add Indicator button
                                ElevatedButton.icon(
                                  onPressed: () {
                                    _showAddIndicatorDialog();
                                  },
                                  icon: Icon(
                                    Icons.add,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    size: 16,
                                  ),
                                  label: Text(
                                    'Add Indicator',
                                    style: TextStyle(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Theme.of(context)
                                        .colorScheme
                                        .primary
                                        .withOpacity(0.1),
                                    foregroundColor:
                                        Theme.of(context).colorScheme.primary,
                                    elevation: 0,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            // Indicator cards
                            indicators.isEmpty
                                ? Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 24.0),
                                    child: Text(
                                      'No indicators added yet. Click "Add Indicator" to add one.',
                                      style: TextStyle(
                                        color: ThemeReflector.textColor(
                                          context,
                                          importance: TextImportance.tertiary,
                                        ),
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  )
                                : Column(
                                    children: indicators
                                        .map((indicator) =>
                                            _buildIndicatorCard(indicator))
                                        .toList(),
                                  ),

                            if (indicators.isNotEmpty)
                              const SizedBox(height: 16),

                            // Minimum indicators required - start from 0
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Minimum Indicator number triggers required for entry',
                                  style: TextStyle(
                                    color: ThemeReflector.textColor(
                                      context,
                                      importance: TextImportance.secondary,
                                    ),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                const SizedBox(height: 12),
                                Row(
                                  children: [
                                    Text(
                                      '${minIndicators.toInt()} out of ${indicators.length}',
                                      style: TextStyle(
                                        color: ThemeReflector.textColor(
                                          context,
                                          importance: TextImportance.primary,
                                        ),
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Expanded(
                                      child: SliderTheme(
                                        data: SliderThemeData(
                                          trackHeight: 6,
                                          activeTrackColor:
                                              ThemeReflector.statusColor(
                                                  context,
                                                  status: StatusType.success),
                                          inactiveTrackColor:
                                              ThemeReflector.surfaceColor(
                                                  context),
                                          thumbColor: Theme.of(context)
                                              .colorScheme
                                              .onPrimary,
                                          thumbShape:
                                              const RoundSliderThumbShape(
                                            enabledThumbRadius: 8,
                                            elevation: 4,
                                          ),
                                          overlayColor:
                                              ThemeReflector.statusColor(
                                                      context,
                                                      status:
                                                          StatusType.success)
                                                  .withOpacity(0.2),
                                        ),
                                        child: Slider(
                                          value: minIndicators,
                                          min: 0,
                                          max: indicators.isEmpty
                                              ? 0
                                              : indicators.length.toDouble(),
                                          divisions: indicators.isNotEmpty
                                              ? indicators.length
                                              : 1,
                                          onChanged: (value) {
                                            setState(() {
                                              minIndicators = value;
                                            });
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),

                            // Advanced Exit Settings Button
                            const SizedBox(height: 24),
                            SizedBox(
                              width: double.infinity,
                              child: OutlinedButton(
                                onPressed: () {
                                  Navigator.pushNamed(
                                      context, AppConstants.advancedExitRoute);
                                },
                                style: OutlinedButton.styleFrom(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 12),
                                  side: BorderSide(
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    width: 1,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: Text(
                                  'Advanced Exit Settings',
                                  style: TextStyle(
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ], // Close inner else clause
                  ], // Close outer else clause
                ],
              ),
            ),
          ),

          // Bottom next button
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: ThemeReflector.surfaceColor(context),
              border: Border(
                top: BorderSide(
                  color: ThemeReflector.dividerColor(context),
                  width: 1,
                ),
              ),
              boxShadow: ThemeReflector.cardShadow(context),
            ),
            child: SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: () {
                  // Validate Grid Range entry type
                  if (botType?.toLowerCase() == 'grid' &&
                      selectedEntryType == 'Range') {
                    if (rangePercentage.isEmpty ||
                        rangePeriods.isEmpty ||
                        double.tryParse(rangePercentage) == null ||
                        int.tryParse(rangePeriods) == null ||
                        double.parse(rangePercentage) <= 0 ||
                        int.parse(rangePeriods) <= 0) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text(
                              'Please enter valid positive numbers for % change and Periods fields.'),
                          backgroundColor: ThemeReflector.statusColor(
                            context,
                            status: StatusType.error,
                          ),
                        ),
                      );
                      return;
                    }
                  }

                  // Validate Price bot entry price
                  if (botType == 'price') {
                    if (_priceController.text.isEmpty ||
                        double.tryParse(_priceController.text) == null ||
                        double.parse(_priceController.text) <= 0) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text(
                              'Please enter a valid positive price for entry condition.'),
                          backgroundColor: ThemeReflector.statusColor(
                            context,
                            status: StatusType.error,
                          ),
                        ),
                      );
                      return;
                    }
                  }

                  // Save entry settings
                  _saveEntrySettings();

                  // Navigate to Exit screen with all route arguments
                  Navigator.pushNamed(context, AppConstants.exitRoute,
                      arguments: {
                        // Pass through all previous route args
                        ...?routeArgs,
                        // Override specific values
                        'isEditing': isEditing,
                        'botId': botId,
                        'botType': botType
                      });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Next',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(
      ThemeData theme, ColorScheme colorScheme, TextTheme textTheme) {
    // Choose image based on bot type
    String imagePath;
    switch (botType?.toLowerCase()) {
      case 'grid':
        imagePath = 'assets/images/grid_entry.png';
        break;
      case 'momentum':
        imagePath = 'assets/images/momentum_entry.png';
        break;
      default:
        imagePath = 'assets/images/trading_entry.png';
        break;
    }

    return Center(
      child: Column(
        children: [
          const SizedBox(height: 16),
          Image.asset(
            imagePath,
            width: 120,
            height: 120,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.show_chart,
                      size: 48,
                      color: colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Entry",
                      style: TextStyle(
                        color: ThemeReflector.textColor(
                          context,
                          importance: TextImportance.primary,
                        ),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  "Entry",
                  style: TextStyle(
                    color: ThemeReflector.textColor(
                      context,
                      importance: TextImportance.primary,
                    ),
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )),
          const SizedBox(height: 16),
          // Step indicator
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleSection({
    required ThemeData theme,
    required String title,
    required bool value,
    required Function(bool) onChanged,
    bool showInfoIcon = false,
  }) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text(
                title,
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.secondary,
                  ),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (showInfoIcon) const SizedBox(width: 7),
              if (showInfoIcon)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.info_outline,
                    color: ThemeReflector.iconColor(context, opacity: 0.6),
                    size: 16,
                  ),
                ),
            ],
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: theme.colorScheme.primary,
            activeTrackColor: theme.colorScheme.primary.withOpacity(0.5),
            inactiveThumbColor: ThemeUtils.isDarkMode(context)
                ? ThemeReflector.surfaceColor(context)
                : Theme.of(context).colorScheme.onPrimary,
            inactiveTrackColor: ThemeUtils.isDarkMode(context)
                ? ThemeReflector.surfaceColor(context).withOpacity(0.5)
                : ThemeReflector.borderColor(context),
          ),
        ],
      ),
    );
  }

  Widget _buildIndicatorCard(Map<String, dynamic> indicator) {
    final String indicatorName = indicator['name'] ?? 'Unknown';
    final String? description = indicator['description'];
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    // Create a formatted string with the indicator parameters
    String parameterText = '';
    if (indicator['period_num'] != null) {
      parameterText += 'Period: ${indicator['period_num']}';
    }

    if (indicator['value2'] != null) {
      parameterText += parameterText.isNotEmpty ? ', ' : '';
      parameterText += 'Value 2: ${indicator['value2']}';
    }

    if (indicator['value3'] != null) {
      parameterText += parameterText.isNotEmpty ? ', ' : '';
      parameterText += 'Value 3: ${indicator['value3']}';
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        border: Border.all(
          color: ThemeReflector.borderColor(context),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color: ThemeReflector.surfaceColor(context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  indicatorName,
                  style: textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Row(
                children: [
                  IconButton(
                    icon: Icon(
                      Icons.settings,
                      color: textTheme.bodyMedium?.color,
                      size: 20,
                    ),
                    onPressed: () {
                      _showIndicatorSettings(indicator);
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 16),
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      color: textTheme.bodySmall?.color,
                      size: 20,
                    ),
                    onPressed: () {
                      setState(() {
                        indicators.remove(indicator);
                        // Adjust min indicators if necessary
                        if (minIndicators > indicators.length) {
                          minIndicators = indicators.length.toDouble();
                        }
                      });
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ],
          ),
          if (parameterText.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                parameterText,
                style: textTheme.bodySmall,
              ),
            ),
          if (description != null && description.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                description,
                style: textTheme.bodySmall?.copyWith(
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Filter indicators based on bot type
  /// Simple bot can show Volume indicator (backend might need it)
  /// Other bot types filter out Volume as per web behavior
  List<Map<String, dynamic>> _getFilteredIndicators() {
    print('Entry Screen - Bot Type: $botType');
    print('Entry Screen - Available indicators: ${availableIndicators.length}');

    if (botType?.toLowerCase() == 'simple') {
      // Simple bot: Show ALL indicators including Volume (backend might need it)
      print(
          'Entry Screen - Simple bot: Showing all indicators including Volume');
      return availableIndicators;
    } else {
      // Other bot types: Filter out Volume indicator as per web behavior
      final filtered = availableIndicators.where((indicator) {
        final name = indicator['name']?.toString().toLowerCase() ?? '';
        return name != 'volume';
      }).toList();

      print('Entry Screen - $botType bot: Filtered out Volume indicator');
      print('Entry Screen - Filtered indicators: ${filtered.length}');
      print(
          'Entry Screen - Available indicators: ${filtered.map((i) => i['name']).toList()}');
      return filtered;
    }
  }

  void _showAddIndicatorDialog() {
    if (availableIndicators.isEmpty && !isLoadingIndicators) {
      _fetchIndicators();
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Add Indicator',
          style: TextStyle(
            color: ThemeReflector.textColor(
              context,
              importance: TextImportance.primary,
            ),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: isLoadingIndicators
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20.0),
                    child: CircularProgressIndicator(),
                  ),
                )
              : availableIndicators.isEmpty
                  ? const Center(
                      child: Text('No indicators available'),
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      itemCount: _getFilteredIndicators().length,
                      itemBuilder: (context, index) {
                        final indicator = _getFilteredIndicators()[index];
                        final String name = indicator['name'] ?? 'Unknown';

                        // Create a formatted string with important parameters
                        String paramInfo =
                            'Period: ${indicator['period_num'] ?? ''}';
                        if (indicator['value2'] != null) {
                          paramInfo += ', Value2: ${indicator['value2']}';
                        }

                        return ListTile(
                          title: Text(name),
                          subtitle: Text(paramInfo),
                          onTap: () {
                            // Validate indicator count against user's subscription plan
                            final authController = Provider.of<AuthController>(
                                context,
                                listen: false);
                            final validation = SubscriptionValidationService
                                .validateIndicators(
                                    authController, indicators.length + 1);

                            if (!validation.isValid) {
                              Navigator.pop(context); // Close the dialog first
                              SubscriptionValidationService.showUpgradeDialog(
                                context,
                                validation.message!,
                                validation.requiredTier!,
                              );
                              return;
                            }

                            setState(() {
                              // Clone the indicator to avoid modifying the original
                              final indicatorToAdd =
                                  Map<String, dynamic>.from(indicator);
                              indicators.add(indicatorToAdd);
                            });
                            Navigator.pop(context);
                          },
                        );
                      },
                    ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: Theme.of(context).colorScheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _showIndicatorSettings(Map<String, dynamic> indicator) {
    // Create controllers for each editable field
    final periodController =
        TextEditingController(text: indicator['period_num']?.toString() ?? '');
    final value2Controller =
        TextEditingController(text: indicator['value2']?.toString() ?? '');
    final value3Controller =
        TextEditingController(text: indicator['value3']?.toString() ?? '');

    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white, // ✅ Set dialog background to white
        title: Text(
          'Edit ${indicator['name']}',
          style: textTheme.titleMedium?.copyWith(color: Colors.black),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Period',
                style: textTheme.bodyMedium?.copyWith(color: Colors.black),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: periodController,
                keyboardType: TextInputType.number,
                style: const TextStyle(color: Colors.black),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: Colors.white, // ✅ Set input background to white
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF007AFF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Value 2',
                style: textTheme.bodyMedium?.copyWith(color: Colors.black),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: value2Controller,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                style: const TextStyle(color: Colors.black),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: Colors.white, // ✅ Set input background to white
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF007AFF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Value 3',
                style: textTheme.bodyMedium?.copyWith(color: Colors.black),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: value3Controller,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                style: const TextStyle(color: Colors.black),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: Colors.white, // ✅ Set input background to white
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF007AFF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: textTheme.bodyMedium?.color),
            ),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                // Update indicator values
                indicator['period_num'] = int.tryParse(periodController.text) ??
                    indicator['period_num'];
                indicator['value2'] = double.tryParse(value2Controller.text) ??
                    indicator['value2'];
                indicator['value3'] = double.tryParse(value3Controller.text) ??
                    indicator['value3'];
              });
              Navigator.pop(context);
            },
            child: Text(
              'Save',
              style: TextStyle(color: theme.colorScheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  // Method to save entry settings
  void _saveEntrySettings() {
    // Get the provider
    final botConfigProvider =
        Provider.of<BotConfigProvider>(context, listen: false);
    final currentConfig = botConfigProvider.botConfig;

    // Format the indicators list for display in UI
    List<String> formattedIndicators = [];
    for (var indicator in indicators) {
      String indicatorText = indicator['name'] ?? 'Unknown';

      // Add parameters if available
      List<String> params = [];
      if (indicator['period_num'] != null) {
        params.add('${indicator['period_num']}');
      }
      if (indicator['value2'] != null) {
        params.add('${indicator['value2']}');
      }
      if (indicator['value3'] != null) {
        params.add('${indicator['value3']}');
      }

      // Add parameters in parentheses if there are any
      if (params.isNotEmpty) {
        indicatorText += ' (${params.join(', ')})';
      }

      formattedIndicators.add(indicatorText);
    }

    // Format daisy chain text
    String daisyChainText;
    if (enableDaisyChain) {
      daisyChainText = isFirstInDaisyChain ? "Activated (First)" : "Activated";
    } else {
      daisyChainText = "Deactivated";
    }

    // Format trading view alerts
    List<String> tradingViewAlerts = [];
    if (integrateTradingView) {
      tradingViewAlerts = ["Alert for Base Order", "Alert for Extra Order"];
    }

    // Format min indicators text
    String minIndicatorsText =
        "${minIndicators.toInt()} out of ${indicators.length} Indicators";

    // Create updated entries
    final updatedEntries = BotEntries(
      orderType: currentConfig.entries.orderType, // Keep existing order type
      baseOrderLimit: currentConfig
          .entries.baseOrderLimit, // Keep existing base order limit
      baseOrderType:
          currentConfig.entries.baseOrderType, // Keep existing base order type
      daisyChain: daisyChainText,
      tradingView: tradingViewAlerts,
      entryIndicators: formattedIndicators,
      minIndicatorsRequired: minIndicatorsText,
      entryType: selectedEntryType, // Save selected entry type
      rangePercentage: selectedEntryType == 'Range' ? rangePercentage : null,
      rangePeriods: selectedEntryType == 'Range' ? rangePeriods : null,
      isRangeLesser: selectedEntryType == 'Range' ? isRangeLesser : null,
      // Price bot specific fields
      priceEntry: botType == 'price'
          ? _priceController.text
          : null, // ✅ Save price entry
      // Sell bot specific fields
      averageEntryPrice:
          botType == 'sell' ? _avgEntryPriceController.text : null,
    );

    // Update the provider
    botConfigProvider.updateEntries(updatedEntries);

    print('Entry settings saved to BotConfigProvider:');
    print('Enable Daisy Chain: $enableDaisyChain');
    print('Is First in Daisy Chain: $isFirstInDaisyChain');
    print('Integrate TradingView: $integrateTradingView');
    print('Indicators: $formattedIndicators');
    print('Minimum Indicators Required: ${minIndicators.toInt()}');
    // Price bot specific debug
    if (botType == 'price') {
      print('Price Entry (Price Bot): ${_priceController.text}');
    }
    // Sell bot specific debug
    if (botType == 'sell') {
      print('Average Entry Price (Sell Bot): ${_avgEntryPriceController.text}');
    }
  }

  // Build price input section for price bot type
  Widget _buildPriceInputSection(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeReflector.surfaceColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeReflector.borderColor(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Entry Condition',
            style: theme.textTheme.titleMedium?.copyWith(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.primary,
              ),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Buy when price is less than or equal to',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.secondary,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _priceController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: 'Enter price',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: ThemeReflector.borderColor(context),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: ThemeReflector.borderColor(context),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: theme.colorScheme.primary,
                        width: 2,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: ThemeReflector.borderColor(context).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: ThemeReflector.borderColor(context),
                  ),
                ),
                child: Text(
                  'USD',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: ThemeReflector.textColor(
                      context,
                      importance: TextImportance.secondary,
                    ),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build average entry price input section for sell bot type
  Widget _buildAverageEntryPriceSection(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeReflector.surfaceColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeReflector.borderColor(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Average Entry Price',
            style: theme.textTheme.titleMedium?.copyWith(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.primary,
              ),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _avgEntryPriceController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: 'Enter average entry price',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: ThemeReflector.borderColor(context),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: ThemeReflector.borderColor(context),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: theme.colorScheme.primary,
                        width: 2,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: ThemeReflector.borderColor(context).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: ThemeReflector.borderColor(context),
                  ),
                ),
                child: Text(
                  'USD',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: ThemeReflector.textColor(
                      context,
                      importance: TextImportance.secondary,
                    ),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build Quickstart-specific Entry UI (simplified)
  Widget _buildQuickstartEntrySection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Divider(height: 1, color: ThemeReflector.dividerColor(context)),

        // Integrate TradingView
        _buildToggleSection(
          theme: theme,
          title: 'Integrate TradingView',
          value: integrateTradingView,
          onChanged: (value) {
            setState(() {
              integrateTradingView = value;
            });
          },
          showInfoIcon: true,
        ),

        Divider(height: 1, color: ThemeReflector.dividerColor(context)),

        // Indicator Triggers Section (sử dụng UI hiện có)
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ThemeReflector.surfaceColor(context),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: ThemeReflector.borderColor(context),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        'Indicator Triggers',
                        style: TextStyle(
                          color: ThemeReflector.textColor(
                            context,
                            importance: TextImportance.secondary,
                          ),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 7),
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.info_outline,
                          color:
                              ThemeReflector.iconColor(context, opacity: 0.6),
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                  // Add Indicator button (giống hệt như UI bình thường)
                  ElevatedButton.icon(
                    onPressed: () {
                      _showAddIndicatorDialog();
                    },
                    icon: Icon(
                      Icons.add,
                      color: Theme.of(context).colorScheme.primary,
                      size: 16,
                    ),
                    label: Text(
                      'Add Indicator',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context)
                          .colorScheme
                          .primary
                          .withOpacity(0.1),
                      foregroundColor: Theme.of(context).colorScheme.primary,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Indicator cards (sử dụng _buildIndicatorCard hiện có)
              indicators.isEmpty
                  ? Padding(
                      padding: const EdgeInsets.symmetric(vertical: 24.0),
                      child: Text(
                        'No indicators added yet. Click "Add Indicator" to add one.',
                        style: TextStyle(
                          color: ThemeReflector.textColor(
                            context,
                            importance: TextImportance.tertiary,
                          ),
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : Column(
                      children: indicators
                          .map((indicator) => _buildIndicatorCard(indicator))
                          .toList(),
                    ),

              if (indicators.isNotEmpty) const SizedBox(height: 16),

              // Minimum indicators required (giống hệt UI bình thường)
              if (indicators.isNotEmpty) ...[
                const SizedBox(height: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Minimum Indicator number triggers required for entry',
                      style: TextStyle(
                        color: ThemeReflector.textColor(
                          context,
                          importance: TextImportance.secondary,
                        ),
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Text(
                          '${minIndicators.toInt()} out of ${indicators.length}',
                          style: TextStyle(
                            color: ThemeReflector.textColor(
                              context,
                              importance: TextImportance.primary,
                            ),
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Expanded(
                          child: SliderTheme(
                            data: SliderThemeData(
                              trackHeight: 6,
                              activeTrackColor: ThemeReflector.statusColor(
                                  context,
                                  status: StatusType.success),
                              inactiveTrackColor:
                                  ThemeReflector.surfaceColor(context),
                              thumbColor:
                                  Theme.of(context).colorScheme.onPrimary,
                              thumbShape: const RoundSliderThumbShape(
                                enabledThumbRadius: 8,
                                elevation: 4,
                              ),
                              overlayColor: ThemeReflector.statusColor(context,
                                      status: StatusType.success)
                                  .withOpacity(0.2),
                            ),
                            child: Slider(
                              value: minIndicators,
                              min: 0,
                              max: indicators.isEmpty
                                  ? 0
                                  : indicators.length.toDouble(),
                              divisions:
                                  indicators.isNotEmpty ? indicators.length : 1,
                              onChanged: (value) {
                                setState(() {
                                  minIndicators = value;
                                });
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  // Build Grid-specific Entry UI
  Widget _buildGridEntrySection(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Entry Type',
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.secondary,
              ),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          GestureDetector(
            onTap: () => _showEntryTypeDialog(),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              decoration: BoxDecoration(
                border: Border.all(
                  color: ThemeReflector.borderColor(context),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    selectedEntryType,
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.primary,
                      ),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: ThemeReflector.iconColor(context),
                    size: 24,
                  ),
                ],
              ),
            ),
          ),

          // Dynamic content based on selected entry type
          const SizedBox(height: 24),
          if (selectedEntryType == 'Indicator')
            ..._buildIndicatorContent(theme),
          if (selectedEntryType == 'Range') ..._buildRangeContent(theme),

          // Trading Frequency (shown for Indicator and Range)
          if (selectedEntryType != 'Immediate') ...[
            const SizedBox(height: 24),
            _buildTradingFrequencySection(theme),
          ],
        ],
      ),
    );
  }

  // Show Entry Type Selection Dialog
  void _showEntryTypeDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: ThemeReflector.surfaceColor(context),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12),
              decoration: BoxDecoration(
                color: ThemeReflector.borderColor(context),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Select Entry Type',
                    style: TextStyle(
                      color: ThemeReflector.textColor(context,
                          importance: TextImportance.primary),
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Icon(
                      Icons.close,
                      color: ThemeReflector.iconColor(context),
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),

            // Options
            ..._buildEntryTypeOptions(),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildEntryTypeOptions() {
    final options = ['Immediate', 'Indicator', 'Range'];

    return options.map((option) {
      final isSelected = selectedEntryType == option;

      return Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              setState(() {
                selectedEntryType = option;
              });
              Navigator.pop(context);
            },
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    option,
                    style: TextStyle(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : ThemeReflector.textColor(context,
                              importance: TextImportance.primary),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                ],
              ),
            ),
          ),
        ),
      );
    }).toList();
  }

  // Build Indicator Content for Grid Entry
  List<Widget> _buildIndicatorContent(ThemeData theme) {
    return [
      // TradingView Toggle
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text(
                'TradingView',
                style: TextStyle(
                  color: ThemeReflector.textColor(context,
                      importance: TextImportance.secondary),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.info_outline,
                size: 16,
                color: ThemeReflector.iconColor(context),
              ),
            ],
          ),
          Switch(
            value: tradingViewEnabled,
            onChanged: (value) {
              setState(() {
                tradingViewEnabled = value;
              });
            },
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),

      const SizedBox(height: 24),

      // Indicator Triggers
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text(
                'Indicator Triggers',
                style: TextStyle(
                  color: ThemeReflector.textColor(context,
                      importance: TextImportance.secondary),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.info_outline,
                size: 16,
                color: ThemeReflector.iconColor(context),
              ),
            ],
          ),
          ElevatedButton.icon(
            onPressed: () {
              // Use same logic for all bot types
              _showAddIndicatorDialog();
            },
            icon: Icon(
              Icons.add,
              color: Theme.of(context).colorScheme.primary,
              size: 16,
            ),
            label: Text(
              'Add Indicator',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  Theme.of(context).colorScheme.primary.withOpacity(0.1),
              foregroundColor: Theme.of(context).colorScheme.primary,
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),

      const SizedBox(height: 12),

      // Indicator Slider
      Row(
        children: [
          Text(
            '${indicatorTriggers.toInt()}',
            style: TextStyle(
              color: ThemeReflector.textColor(context,
                  importance: TextImportance.primary),
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'of ${indicators.length}',
            style: TextStyle(
              color: ThemeReflector.textColor(context,
                  importance: TextImportance.secondary),
              fontSize: 16,
            ),
          ),
          const Spacer(),
          Expanded(
            flex: 3,
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: 6,
                activeTrackColor: Theme.of(context).colorScheme.primary,
                inactiveTrackColor:
                    ThemeReflector.surfaceColor(context).withOpacity(0.5),
                thumbColor: Theme.of(context).colorScheme.onPrimary,
                thumbShape: const RoundSliderThumbShape(
                  enabledThumbRadius: 8,
                  elevation: 4,
                ),
                overlayColor:
                    Theme.of(context).colorScheme.primary.withOpacity(0.2),
              ),
              child: Slider(
                value: indicatorTriggers,
                min: 0,
                max: indicators.length.toDouble(),
                divisions: indicators.isNotEmpty ? indicators.length : 1,
                onChanged: (value) {
                  setState(() {
                    indicatorTriggers = value;
                  });
                },
              ),
            ),
          ),
        ],
      ),

      // Show indicator cards if any indicators are added
      if (indicators.isNotEmpty) ...[
        const SizedBox(height: 16),
        ...indicators
            .map((indicator) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: _buildIndicatorCard(indicator),
                ))
            .toList(),
      ],
    ];
  }

  // Build Range Content for Grid Entry
  List<Widget> _buildRangeContent(ThemeData theme) {
    return [
      Text(
        'Create Grid if the price difference is',
        style: TextStyle(
          color: ThemeReflector.textColor(context,
              importance: TextImportance.secondary),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),

      const SizedBox(height: 16),

      // Lesser/Greater Radio Buttons
      Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isRangeLesser = true;
                });
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isRangeLesser
                      ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isRangeLesser
                        ? Theme.of(context).colorScheme.primary
                        : ThemeReflector.borderColor(context),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isRangeLesser
                              ? Theme.of(context).colorScheme.primary
                              : ThemeReflector.borderColor(context),
                          width: 2,
                        ),
                      ),
                      child: isRangeLesser
                          ? Center(
                              child: Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Lesser',
                      style: TextStyle(
                        color: ThemeReflector.textColor(context,
                            importance: TextImportance.primary),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isRangeLesser = false;
                });
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: !isRangeLesser
                      ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: !isRangeLesser
                        ? Theme.of(context).colorScheme.primary
                        : ThemeReflector.borderColor(context),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: !isRangeLesser
                              ? Theme.of(context).colorScheme.primary
                              : ThemeReflector.borderColor(context),
                          width: 2,
                        ),
                      ),
                      child: !isRangeLesser
                          ? Center(
                              child: Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Greater',
                      style: TextStyle(
                        color: ThemeReflector.textColor(context,
                            importance: TextImportance.primary),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),

      const SizedBox(height: 24),

      // Percentage and Periods Input
      Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '% change',
                  style: TextStyle(
                    color: ThemeReflector.textColor(context,
                        importance: TextImportance.secondary),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: TextEditingController(text: rangePercentage),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 14),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                          color: ThemeReflector.borderColor(context)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                          color: ThemeReflector.borderColor(context)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: theme.colorScheme.primary),
                    ),
                    hintText: '5%',
                  ),
                  onChanged: (value) {
                    rangePercentage = value;
                  },
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Periods',
                  style: TextStyle(
                    color: ThemeReflector.textColor(context,
                        importance: TextImportance.secondary),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: TextEditingController(text: rangePeriods),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 14),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                          color: ThemeReflector.borderColor(context)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                          color: ThemeReflector.borderColor(context)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: theme.colorScheme.primary),
                    ),
                    hintText: '3 candles',
                  ),
                  onChanged: (value) {
                    rangePeriods = value;
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    ];
  }

  // Build Trading Frequency Section
  Widget _buildTradingFrequencySection(ThemeData theme) {
    final frequencies = ['1m', '5m', '15m', '1H', '2H', '4H', '1D'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Trading Frequency',
              style: TextStyle(
                color: ThemeReflector.textColor(context,
                    importance: TextImportance.secondary),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.info_outline,
              size: 16,
              color: ThemeReflector.iconColor(context),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: frequencies.map((frequency) {
            final isSelected = selectedTradingFrequency == frequency;

            return GestureDetector(
              onTap: () {
                setState(() {
                  selectedTradingFrequency = frequency;
                });
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : ThemeReflector.borderColor(context),
                    width: 1,
                  ),
                ),
                child: Text(
                  frequency,
                  style: TextStyle(
                    color: isSelected
                        ? Colors.white
                        : ThemeReflector.textColor(context,
                            importance: TextImportance.primary),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  // Build Momentum-specific Entry UI - NO INDICATORS, NO TRADING VIEW
  Widget _buildMomentumEntrySection(ThemeData theme) {
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // ENTER BASE ORDER Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'ENTER BASE ORDER',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.secondary,
                  ),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'If price increases by',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: '0%',
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: ThemeReflector.borderColor(context), width: 1.0),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: ThemeReflector.borderColor(context), width: 1.0),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        BorderSide(color: colorScheme.primary, width: 1.0),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'From price captured at bot\'s start time.',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.secondary,
                  ),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),

        // ENTER EXTRA ORDER Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'ENTER EXTRA ORDER',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.secondary,
                  ),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'If price continues to increase by',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: '0%',
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: ThemeReflector.borderColor(context), width: 1.0),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: ThemeReflector.borderColor(context), width: 1.0),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        BorderSide(color: colorScheme.primary, width: 1.0),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'From price captured at bot\'s start time.',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.secondary,
                  ),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),

        // Add Indicator Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'INDICATORS',
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.secondary,
                      ),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Use same logic as Grid bot
                      _showAddIndicatorDialog();
                    },
                    icon: Icon(
                      Icons.add,
                      color: colorScheme.primary,
                      size: 16,
                    ),
                    label: Text(
                      'Add Indicator',
                      style: TextStyle(
                        color: colorScheme.primary,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          colorScheme.primary.withValues(alpha: 0.1),
                      foregroundColor: colorScheme.primary,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Show indicator cards if any indicators are added (same as Grid bot)
              if (indicators.isNotEmpty) ...[
                ...indicators
                    .map((indicator) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: _buildIndicatorCard(indicator),
                        ))
                    .toList(),
              ] else
                Text(
                  'No indicators added yet. Click "Add Indicator" to add one.',
                  style: TextStyle(
                    color: ThemeReflector.textColor(
                      context,
                      importance: TextImportance.secondary,
                    ),
                    fontSize: 14,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
